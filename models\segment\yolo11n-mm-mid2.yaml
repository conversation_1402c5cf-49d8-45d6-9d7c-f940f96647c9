# Ultralytics YOLO11 🚀 MultiModal Triple Mid-level Fusion Configuration
# YOLOMM RGB+X二元模态双层级中期融合配置
# 策略：RGB和X模态独立特征提取，在P3和P4两个层级进行融合
# 适用场景：需要多层级特征融合，充分利用不同尺度的模态互补信息

# Parameters
nc: 80 # number of classes
scales: # model compound scaling constants, i.e. 'model=yolo11n.yaml' will call yolo11.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.50, 0.25, 1024] # YOLO11n summary: 319 layers, 2624080 parameters, 2624064 gradients, 6.6 GFLOPs
  s: [0.50, 0.50, 1024] # YOLO11s summary: 319 layers, 9458752 parameters, 9458736 gradients, 21.7 GFLOPs
  m: [0.50, 1.00, 512]  # YOLO11m summary: 409 layers, 20114688 parameters, 20114672 gradients, 68.5 GFLOPs
  l: [1.00, 1.00, 512]  # YOLO11l summary: 631 layers, 25372160 parameters, 25372144 gradients, 87.6 GFLOPs
  x: [1.00, 1.50, 512]  # YOLO11x summary: 631 layers, 56966176 parameters, 56966160 gradients, 196.4 GFLOPs

# 多模态架构定义 - 双层级中期融合
# X模态类型由数据集配置或训练时指定，模型架构保持通用性

# 双路径 backbone - RGB路径和X路径独立提取特征
backbone:
  # RGB路径到P3 (层0-4) - 提取RGB特征到P3级别
  - [-1, 1, Conv, [64, 3, 2], 'RGB']      # 0-P1/2 RGB路径起始
  - [-1, 1, Conv, [128, 3, 2]]            # 1-P2/4
  - [-1, 2, C3k2, [256, False, 0.25]]     # 2
  - [-1, 1, Conv, [256, 3, 2]]            # 3-P3/8  
  - [-1, 2, C3k2, [512, False, 0.25]]     # 4 RGB P3特征

  # X路径到P3 (层5-9) - 提取X模态特征到P3级别
  - [-1, 1, Conv, [64, 3, 2], 'X']        # 5-P1/2 X路径起始
  - [-1, 1, Conv, [128, 3, 2]]            # 6-P2/4
  - [-1, 2, C3k2, [256, False, 0.25]]     # 7
  - [-1, 1, Conv, [256, 3, 2]]            # 8-P3/8
  - [-1, 2, C3k2, [512, False, 0.25]]     # 9 X P3特征

  # 第一次中期融合层 (层10-12) - 融合RGB和X的P3级特征
  - [[4, 9], 1, Concat, [1]]              # 10 融合RGB(层4)和X(层9)的P3特征
  - [-1, 2, C3k2, [512, True]]            # 11 融合特征处理
  - [-1, 1, C2PSA, [512]]                 # 12 注意力增强P3融合特征

  # RGB路径继续到P4 (层13-15)
  - [4, 1, Conv, [512, 3, 2]]             # 13-P4/16 从RGB P3继续
  - [-1, 2, C3k2, [512, True]]            # 14
  - [-1, 1, SPPF, [512, 5]]               # 15 RGB P4特征

  # X路径继续到P4 (层16-18)
  - [9, 1, Conv, [512, 3, 2]]             # 16-P4/16 从X P3继续
  - [-1, 2, C3k2, [512, True]]            # 17
  - [-1, 1, SPPF, [512, 5]]               # 18 X P4特征

  # 第二次中期融合层 (层19-21) - 融合RGB和X的P4级特征
  - [[15, 18], 1, Concat, [1]]            # 19 融合RGB(层15)和X(层18)的P4特征
  - [-1, 2, C3k2, [1024, True]]           # 20 融合特征处理
  - [-1, 2, C2PSA, [1024]]                # 21 注意力增强P4融合特征

# 检测头 - 基于双层级融合特征的多尺度检测头
head:
  # P4/16分支 - 从P4融合特征开始
  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]   # 22 P4融合特征上采样 40×40 → 80×80
  - [[-1, 12], 1, Concat, [1]]                   # 23 与P3融合特征连接
  - [-1, 2, C3k2, [512, False]]                  # 24 处理P4特征 (80×80)

  # P3/8分支 - 继续上采样
  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]   # 25 继续上采样 80×80 → 160×160  
  - [-1, 2, C3k2, [256, False]]                  # 26 处理P3特征 (160×160)

  # P4/16分支回连
  - [-1, 1, Conv, [256, 3, 2]]                   # 27 下采样 160×160 → 80×80
  - [[-1, 24], 1, Concat, [1]]                   # 28 融合P4特征 (80×80 + 80×80)
  - [-1, 2, C3k2, [512, False]]                  # 29 最终P4特征 (80×80)

  # P5/32分支回连  
  - [-1, 1, Conv, [512, 3, 2]]                   # 30 下采样 80×80 → 40×40
  - [[-1, 21], 1, Concat, [1]]                   # 31 融合原始P4融合特征 (40×40 + 40×40)
  - [-1, 2, C3k2, [1024, False]]                 # 32 最终P5特征 (40×40)

  - [[26, 29, 32], 1, Detect, [nc]]              # 33 Detect(P3, P4, P5)
