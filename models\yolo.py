# ========================= 顶部环境设置（按你的要求保留） =========================
import os
os.environ["QT_QPA_PLATFORM_PLUGIN_PATH"] = "/home/<USER>/anaconda3/envs/py39/lib/qt/plugins"

# ========================= 标准库与第三方依赖 =========================
import argparse
import contextlib
import math
import os as _os  # 避免与上面的 os 混淆
import platform
import sys
from copy import deepcopy
from pathlib import Path

import torch
import torch.nn as nn

# ========================= 项目内导入 =========================
from .router import InputRouter  # ★ 多模态命名输入路由层（从多输入中取 'RGB' 或 'X' 等）
FILE = Path(__file__).resolve()
ROOT = FILE.parents[1]  # YOLOv5 根目录
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))
if platform.system() != "Windows":
    ROOT = Path(_os.path.relpath(ROOT, Path.cwd()))  # 使用相对路径

from models.common import (
    C3,
    C3SPP,
    C3TR,
    SPP,
    SPPF,
    Bottleneck,
    BottleneckCSP,
    C3Ghost,
    C3x,
    Concat,
    Contract,
    Conv,
    CrossConv,
    DetectMultiBackend,
    DWConv,
    DWConvTranspose2d,
    Expand,
    Focus,
    GhostBottleneck,
    GhostConv,
    Proto,
)
from models.experimental import MixConv2d
from utils.autoanchor import check_anchor_order
from utils.general import LOGGER, check_version, check_yaml, colorstr, make_divisible, print_args
from utils.plots import feature_visualization
from utils.torch_utils import (
    fuse_conv_and_bn,
    initialize_weights,
    model_info,
    profile,
    scale_img,
    select_device,
    time_sync,
)

try:
    import thop  # 计算 FLOPs
except ImportError:
    thop = None


# ========================= 检测与分割头（保持原逻辑） =========================
class Detect(nn.Module):
    """YOLOv5 检测头（框）"""

    stride = None   # 构建时推理得到的步长
    dynamic = False # 强制重建网格
    export = False  # 导出模式

    def __init__(self, nc=80, anchors=(), ch=(), inplace=True):
        super().__init__()
        self.nc = nc                          # 类别数
        self.no = nc + 5                      # 每个锚点的输出维度
        self.nl = len(anchors)                # 检测层数量
        self.na = len(anchors[0]) // 2        # 每层的锚点数
        self.grid = [torch.empty(0) for _ in range(self.nl)]         # 网格缓存
        self.anchor_grid = [torch.empty(0) for _ in range(self.nl)]  # 锚点网格缓存
        self.register_buffer("anchors", torch.tensor(anchors).float().view(self.nl, -1, 2))
        self.m = nn.ModuleList(nn.Conv2d(x, self.no * self.na, 1) for x in ch)  # 输出卷积
        self.inplace = inplace

    def forward(self, x):
        """训练返回特征图（供 loss 用），推理返回拼接后的预测。"""
        z = []
        for i in range(self.nl):
            x[i] = self.m[i](x[i])  # 1x1 conv
            bs, _, ny, nx = x[i].shape
            x[i] = x[i].view(bs, self.na, self.no, ny, nx).permute(0, 1, 3, 4, 2).contiguous()

            if not self.training:  # 推理
                if self.dynamic or self.grid[i].shape[2:4] != x[i].shape[2:4]:
                    self.grid[i], self.anchor_grid[i] = self._make_grid(nx, ny, i)

                if isinstance(self, Segment):  # 分割头（boxes + masks）
                    xy, wh, conf, mask = x[i].split((2, 2, self.nc + 1, self.no - self.nc - 5), 4)
                    xy = (xy.sigmoid() * 2 + self.grid[i]) * self.stride[i]
                    wh = (wh.sigmoid() * 2) ** 2 * self.anchor_grid[i]
                    y = torch.cat((xy, wh, conf.sigmoid(), mask), 4)
                else:  # 检测头（boxes）
                    xy, wh, conf = x[i].sigmoid().split((2, 2, self.nc + 1), 4)
                    xy = (xy * 2 + self.grid[i]) * self.stride[i]
                    wh = (wh * 2) ** 2 * self.anchor_grid[i]
                    y = torch.cat((xy, wh, conf), 4)
                z.append(y.view(bs, self.na * nx * ny, self.no))
        return x if self.training else (torch.cat(z, 1),) if self.export else (torch.cat(z, 1), x)

    def _make_grid(self, nx=20, ny=20, i=0, torch_1_10=check_version(torch.__version__, "1.10.0")):
        """构建网格（兼容 torch<1.10 的 meshgrid 接口差异）"""
        d = self.anchors[i].device
        t = self.anchors[i].dtype
        shape = 1, self.na, ny, nx, 2
        y, x = torch.arange(ny, device=d, dtype=t), torch.arange(nx, device=d, dtype=t)
        yv, xv = torch.meshgrid(y, x, indexing="ij") if torch_1_10 else torch.meshgrid(y, x)
        grid = torch.stack((xv, yv), 2).expand(shape) - 0.5
        anchor_grid = (self.anchors[i] * self.stride[i]).view((1, self.na, 1, 1, 2)).expand(shape)
        return grid, anchor_grid


class Segment(Detect):
    """YOLOv5 分割头（在检测头基础上增加 Proto 原型）"""

    def __init__(self, nc=80, anchors=(), nm=32, npr=256, ch=(), inplace=True):
        super().__init__(nc, anchors, ch, inplace)
        self.nm = nm          # mask 数量
        self.npr = npr        # proto 通道
        self.no = 5 + nc + self.nm
        self.m = nn.ModuleList(nn.Conv2d(x, self.no * self.na, 1) for x in ch)
        self.proto = Proto(ch[0], self.npr, self.nm)
        self.detect = Detect.forward

    def forward(self, x):
        """训练返回 (preds, proto)，推理/导出时适配输出格式"""
        p = self.proto(x[0])
        x = self.detect(self, x)
        return (x, p) if self.training else (x[0], p) if self.export else (x[0], p, x[1])


# ========================= 基础模型与前向重构（适配 InputRouter） =========================
class BaseModel(nn.Module):
    """YOLOv5 基础模型：这里对 _forward_once 做了**最小**改动，显式支持 InputRouter。"""

    def forward(self, x, profile=False, visualize=False):
        """
        x 可为：
          - 单路张量：Tensor [B, C, H, W]
          - 多路张量：Tuple/List[Tensor, Tensor] 或 Dict[str, Tensor]（例如 ('RGB','X')）
        """
        return self._forward_once(x, profile, visualize)

    def _forward_once(self, x, profile=False, visualize=False):
        """
        前向单尺度推理/训练。
        关键改动：
          1) 保留原有 from 路由逻辑；
          2) 若当前层是 InputRouter，则**忽略 from**，直接把“全量多输入”交给该层；
          3) 其它层行为不变。
        """
        multi_inputs = x  # 保存“多输入”源，供 InputRouter 使用
        y, dt = [], []    # y 存缓存输出；dt 存每层 profile 计时

        # 为避免第一层就用到“上一层 x”的歧义，这里局部变量 prev_in 仅用于 profile 打印
        prev_in = None

        for m in self.model:
            # === 处理来源路由（除 InputRouter 外，保持原逻辑） ===
            if not isinstance(m, InputRouter):
                if m.f != -1:
                    x = y[m.f] if isinstance(m.f, int) else [x if j == -1 else y[j] for j in m.f]
                # 记录 profile 输入
                prev_in = x
            else:
                # InputRouter 直接吃多输入（tuple/list/dict）
                x = m(multi_inputs)
                prev_in = x  # 给 profile 用

            # === Profile（可选） ===
            if profile:
                self._profile_one_layer(m, prev_in, dt, multi_inputs if isinstance(m, InputRouter) else None)

            # === 前向计算 ===
            # 对 InputRouter 已经前向过一次（上面 m(multi_inputs)），这里不再重复计算
            if not isinstance(m, InputRouter):
                x = m(x)

            # === 保存需要的层输出（与 YOLO 原逻辑保持一致） ===
            y.append(x if m.i in self.save else None)

            # === 可视化（可选） ===
            if visualize:
                feature_visualization(x, m.type, m.i, save_dir=visualize)

        return x

    def _profile_one_layer(self, m, x, dt, multi_inputs_for_router=None):
        """每层 profile：计算 GFLOPs / 时间 / 参数量（对 InputRouter 特判其输入形态）。"""
        c = m == self.model[-1]  # 是否最后一层
        # 针对 InputRouter：其“输入”应该是多输入，而非上游特征
        run_in = multi_inputs_for_router if isinstance(m, InputRouter) else x
        try:
            o = thop.profile(m, inputs=(run_in.copy() if c else run_in,), verbose=False)[0] / 1e9 * 2 if thop else 0
        except Exception:
            o = 0  # 某些层/输入类型 thop 可能不支持，忽略 FLOPs
        t = time_sync()
        for _ in range(10):
            m(run_in.copy() if c else run_in)
        dt.append((time_sync() - t) * 100)
        if m == self.model[0]:
            LOGGER.info(f"{'time (ms)':>10s} {'GFLOPs':>10s} {'params':>10s}  module")
        LOGGER.info(f"{dt[-1]:10.2f} {o:10.2f} {m.np:10.0f}  {m.type}")
        if c:
            LOGGER.info(f"{sum(dt):10.2f} {'-':>10s} {'-':>10s}  Total")

    def fuse(self):
        """Conv+BN 融合（保持原逻辑）"""
        LOGGER.info("Fusing layers... ")
        for m in self.model.modules():
            if isinstance(m, (Conv, DWConv)) and hasattr(m, "bn"):
                m.conv = fuse_conv_and_bn(m.conv, m.bn)
                delattr(m, "bn")
                m.forward = m.forward_fuse
        self.info()
        return self

    def info(self, verbose=False, img_size=640):
        """打印模型信息"""
        model_info(self, verbose, img_size)

    def _apply(self, fn):
        """重载 .to() / .cuda() / .half() 等的行为，确保 Detect/Segment 的 buffer 同步"""
        self = super()._apply(fn)
        m = self.model[-1]
        if isinstance(m, (Detect, Segment)):
            m.stride = fn(m.stride)
            m.grid = list(map(fn, m.grid))
            if isinstance(m.anchor_grid, list):
                m.anchor_grid = list(map(fn, m.anchor_grid))
        return self


# ========================= 检测/分割模型封装（构建步长时支持多输入） =========================
class DetectionModel(BaseModel):
    """YOLOv5 检测模型：构建时自动解析 YAML，并对含 InputRouter 的模型提供“哑输入”以构建 stride。"""

    def __init__(self, cfg="yolov5s.yaml", ch=3, nc=None, anchors=None):
        super().__init__()
        if isinstance(cfg, dict):
            self.yaml = cfg
        else:
            import yaml
            self.yaml_file = Path(cfg).name
            with open(cfg, encoding="ascii", errors="ignore") as f:
                self.yaml = yaml.safe_load(f)

        # 覆盖 YAML 中的通道/类别/锚框
        ch = self.yaml["ch"] = self.yaml.get("ch", ch)
        if nc and nc != self.yaml["nc"]:
            LOGGER.info(f"Overriding model.yaml nc={self.yaml['nc']} with nc={nc}")
            self.yaml["nc"] = nc
        if anchors:
            LOGGER.info(f"Overriding model.yaml anchors with anchors={anchors}")
            self.yaml["anchors"] = round(anchors)

        # 解析模型
        self.model, self.save = parse_model(deepcopy(self.yaml), ch=[ch])
        self.names = [str(i) for i in range(self.yaml["nc"])]
        self.inplace = self.yaml.get("inplace", True)

        # 构建 stride / anchors（★ 兼容含 InputRouter 的模型：使用多路“哑输入”进行一次前向）
        m = self.model[-1]  # Detect()/Segment()
        if isinstance(m, (Detect, Segment)):

            # 内部函数：做一次前向以获取 stride
            def _forward(x):
                return self.forward(x)[0] if isinstance(m, Segment) else self.forward(x)

            s = 256  # 2x 最小步长
            m.inplace = self.inplace

            # === 判断是否存在 InputRouter（多输入模型） ===
            has_router = any(isinstance(mod, InputRouter) for mod in self.model.modules())
            if has_router:
                # 默认假设两路输入都为 ch 通道，若你的路由层在 router.py 里支持其他通道，可自行调整此处
                dummy_rgb = torch.zeros(1, ch, s, s)
                dummy_x   = torch.zeros(1, ch, s, s)
                dummy_in  = (dummy_rgb, dummy_x)  # 也可用 {'RGB':..., 'X':...} 与 YAML 呼应
                m.stride = torch.tensor([s / feat.shape[-2] for feat in _forward(dummy_in)])
            else:
                dummy = torch.zeros(1, ch, s, s)
                m.stride = torch.tensor([s / feat.shape[-2] for feat in _forward(dummy)])

            check_anchor_order(m)
            m.anchors /= m.stride.view(-1, 1, 1)
            self.stride = m.stride
            self._initialize_biases()  # 仅一次

        # 初始化权重
        initialize_weights(self)
        self.info()
        LOGGER.info("")

    def forward(self, x, augment=False, profile=False, visualize=False):
        """保持原接口：支持单尺度或增广推理。"""
        if augment:
            return self._forward_augment(x)
        return self._forward_once(x, profile, visualize)

    def _forward_augment(self, x):
        """
        增广推理（注意：若为多输入，需对每路做相同几何变换。这里提供基础适配。）
        """
        # 推断输入类型
        def _make_scaled(inp, si, fi):
            if isinstance(inp, torch.Tensor):
                return scale_img(inp.flip(fi) if fi else inp, si, gs=int(self.stride.max()))
            elif isinstance(inp, (tuple, list)):
                return tuple(scale_img(t.flip(fi) if fi else t, si, gs=int(self.stride.max())) for t in inp)
            elif isinstance(inp, dict):
                return {k: scale_img(v.flip(fi) if fi else v, si, gs=int(self.stride.max())) for k, v in inp.items()}
            else:
                raise TypeError("Unsupported input type in _forward_augment.")

        # 统一取图像尺寸（按单路 Tensor 的写法；多输入场景默认两路同尺寸）
        img_size = x.shape[-2:] if isinstance(x, torch.Tensor) else (
            x[0].shape[-2:] if isinstance(x, (tuple, list)) else next(iter(x.values())).shape[-2:]
        )

        s = [1, 0.83, 0.67]
        f = [None, 3, None]
        y = []
        for si, fi in zip(s, f):
            xi = _make_scaled(x, si, fi)
            yi = self._forward_once(xi)[0]
            yi = self._descale_pred(yi, fi, si, img_size)
            y.append(yi)
        y = self._clip_augmented(y)
        return torch.cat(y, 1), None

    def _descale_pred(self, p, flips, scale, img_size):
        """增广逆变换"""
        if self.inplace:
            p[..., :4] /= scale
            if flips == 2:
                p[..., 1] = img_size[0] - p[..., 1]
            elif flips == 3:
                p[..., 0] = img_size[1] - p[..., 0]
        else:
            x, y, wh = p[..., 0:1] / scale, p[..., 1:2] / scale, p[..., 2:4] / scale
            if flips == 2:
                y = img_size[0] - y
            elif flips == 3:
                x = img_size[1] - x
            p = torch.cat((x, y, wh, p[..., 4:]), -1)
        return p

    def _clip_augmented(self, y):
        """裁剪增广尾部（保持原逻辑）"""
        nl = self.model[-1].nl
        g = sum(4**x for x in range(nl))
        e = 1
        i = (y[0].shape[1] // g) * sum(4**x for x in range(e))
        y[0] = y[0][:, :-i]
        i = (y[-1].shape[1] // g) * sum(4 ** (nl - 1 - x) for x in range(e))
        y[-1] = y[-1][:, i:]
        return y

    def _initialize_biases(self, cf=None):
        """初始化 Detect() 偏置（保持原逻辑）"""
        m = self.model[-1]
        for mi, s in zip(m.m, m.stride):
            b = mi.bias.view(m.na, -1)
            b.data[:, 4] += math.log(8 / (640 / s) ** 2)  # obj
            b.data[:, 5 : 5 + m.nc] += (
                math.log(0.6 / (m.nc - 0.99999)) if cf is None else torch.log(cf / cf.sum())
            )
            mi.bias = torch.nn.Parameter(b.view(-1), requires_grad=True)


Model = DetectionModel  # 兼容旧写法
class SegmentationModel(DetectionModel):
    """分割模型（承接 DetectionModel）"""
    def __init__(self, cfg="yolov5s-seg.yaml", ch=3, nc=None, anchors=None):
        super().__init__(cfg, ch, nc, anchors)


# ========================= 解析 YAML 的模型构建（★ 支持 InputRouter） =========================
def parse_model(d, ch):
    """
    从字典 d 解析 YOLOv5 模型。
    关键改动：
      - 新增对 InputRouter 的解析分支：不依赖 from/上游通道，显式输出 c2（默认 3，或由 args 指定）。
      - 其它模块保持原逻辑。
    """
    LOGGER.info(f"\n{'':>3}{'from':>18}{'n':>3}{'params':>10}  {'module':<40}{'arguments':<30}")
    anchors, nc, gd, gw, act, ch_mul = (
        d["anchors"],
        d["nc"],
        d["depth_multiple"],
        d["width_multiple"],
        d.get("activation"),
        d.get("channel_multiple"),
    )
    if act:
        Conv.default_act = eval(act)  # 如 nn.SiLU()
        LOGGER.info(f"{colorstr('activation:')} {act}")
    if not ch_mul:
        ch_mul = 8

    na = (len(anchors[0]) // 2) if isinstance(anchors, list) else anchors  # 每层 anchor 数
    no = na * (nc + 5)

    layers, save, c2 = [], [], ch[-1]
    for i, (f, n, m, args) in enumerate(d["backbone"] + d["head"]):
        m = eval(m) if isinstance(m, str) else m
        # 把字符串参数 eval 掉（如 "nn.SiLU()"）
        for j, a in enumerate(args):
            with contextlib.suppress(NameError):
                args[j] = eval(a) if isinstance(a, str) else a

        n = n_ = max(round(n * gd), 1) if n > 1 else n  # 深度倍率

        # ================= 模块类型分支 =================
        if m in {
            Conv, GhostConv, Bottleneck, GhostBottleneck, SPP, SPPF, DWConv, MixConv2d,
            Focus, CrossConv, BottleneckCSP, C3, C3TR, C3SPP, C3Ghost,
            nn.ConvTranspose2d, DWConvTranspose2d, C3x,
        }:
            c1, c2 = ch[f], args[0]                      # 输入/输出通道
            if c2 != no:                                 # 若不是 Detect/Segment 的输出层
                c2 = make_divisible(c2 * gw, ch_mul)
            args = [c1, c2, *args[1:]]                   # 形如 Conv(c1,c2,k,s,...)
            if m in {BottleneckCSP, C3, C3TR, C3Ghost, C3x}:
                args.insert(2, n)                        # 将重复次数插入到 args
                n = 1

        elif m is nn.BatchNorm2d:
            args = [ch[f]]
            c2 = ch[f]

        elif m is Concat:
            c2 = sum(ch[x] for x in f)

        elif m in {Detect, Segment}:
            # 对 Detect/Segment：args 最后追加各输入分支的通道列表
            args.append([ch[x] for x in f])
            if isinstance(args[1], int):  # 整数锚点数 -> 展开为伪 anchors
                args[1] = [list(range(args[1] * 2))] * len(f)
            if m is Segment:
                # 调整原型维度 npr 的通道
                args[3] = make_divisible(args[3] * gw, ch_mul)

        elif m is Contract:
            c2 = ch[f] * args[0] ** 2

        elif m is Expand:
            c2 = ch[f] // args[0] ** 2

        # ★★★★★ 新增：InputRouter（命名输入路由层） ★★★★★
        elif m is InputRouter:
            """
            InputRouter 行为：
              - 忽略 from（即便 YAML 写了 -1），其 forward 会接收“多输入”源（tuple/list/dict）并选择对应模态。
              - 输出通道 c2 与原始输入模态的通道一致。默认按 3 通道；若 YAML 写法为 ['RGB', 1]，则使用 1。
            YAML 示例：
              - [-1, 1, InputRouter, ['RGB']]        # 默认 3 通道
              - [-1, 1, InputRouter, ['X', 1]]       # 指定 1 通道（如深度/IR）
            """
            # 解析期无法真实获知该模态的通道数，这里策略：
            #   - 若第二个参数提供了 int 通道数，采用之；
            #   - 否则默认 3。
            if len(args) >= 2 and isinstance(args[1], int):
                c2 = args[1]
            else:
                c2 = 3

        else:
            # 其它自定义层，默认以上游通道不变
            c2 = ch[f]

        # 构建实例（重复 n 次的展开方式与原版一致）
        m_ = nn.Sequential(*(m(*args) for _ in range(n))) if n > 1 else m(*args)
        t = str(m)[8:-2].replace("__main__.", "")
        np_ = sum(x_.numel() for x_ in m_.parameters())

        # 附加元信息
        m_.i, m_.f, m_.type, m_.np = i, f, t, np_
        LOGGER.info(f"{i:>3}{str(f):>18}{n_:>3}{np_:10.0f}  {t:<40}{str(args):<30}")

        # 记录需要保存的层索引（与 YOLO 原逻辑一致）
        save.extend(x % i for x in ([f] if isinstance(f, int) else f) if x != -1)

        # 收集层 & 更新通道列表
        layers.append(m_)
        if i == 0:
            ch = []  # 第 0 层后重置，按 YOLO 习惯开始记录每层的输出通道
        ch.append(c2)

    return nn.Sequential(*layers), sorted(save)
