#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多模态YOLOv5模型测试脚本

功能：
1. 测试多模态分割模型的加载
2. 验证InputRouter的多输入路由功能
3. 测试模型的前向推理
4. 验证输出格式的正确性

使用方法：
    python test_multimodal_model.py
"""

import os
import sys
import torch
import numpy as np
from pathlib import Path
import yaml
import time
from typing import Dict, Tuple, Union

# 添加项目根目录到路径
FILE = Path(__file__).resolve()
ROOT = FILE.parents[0]  # YOLOv5 根目录
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))

# 导入项目模块
from models.yolo import SegmentationModel, DetectionModel
from models.router import InputRouter
from utils.general import LOGGER, colorstr
from utils.torch_utils import select_device


class MultiModalModelTester:
    """多模态模型测试器"""
    
    def __init__(self, config_path: str = "models/segment/yolov5n-mid-seg.yaml", device: str = "cpu"):
        """
        初始化测试器
        
        Args:
            config_path: 模型配置文件路径
            device: 运行设备 ('cpu', 'cuda', 'auto')
        """
        self.config_path = config_path
        self.device = select_device(device)
        self.model = None
        
        # 测试参数
        self.img_size = 640
        self.batch_size = 1
        self.channels = 3
        
        LOGGER.info(f"Initializing multimodal model tester")
        LOGGER.info(f"Config file: {config_path}")
        LOGGER.info(f"Device: {self.device}")
    
    def load_model(self) -> bool:
        """
        加载多模态分割模型
        
        Returns:
            bool: 加载是否成功
        """
        try:
            LOGGER.info(f"Loading model config: {self.config_path}")
            
            # Check if config file exists
            if not Path(self.config_path).exists():
                LOGGER.error(f"Config file not found: {self.config_path}")
                return False
            
            # Load model
            self.model = SegmentationModel(cfg=self.config_path, ch=self.channels)
            self.model.to(self.device)
            self.model.eval()
            
            LOGGER.info(f"✅ Model loaded successfully")
            LOGGER.info(f"Model parameters: {sum(p.numel() for p in self.model.parameters()):,}")
            
            return True
            
        except Exception as e:
            LOGGER.error(f"❌ Model loading failed: {e}")
            return False
    
    def create_test_inputs(self) -> Dict[str, torch.Tensor]:
        """
        创建测试输入数据
        
        Returns:
            Dict[str, torch.Tensor]: 包含RGB和X模态的测试数据
        """
        # 创建随机测试数据
        rgb_input = torch.randn(self.batch_size, self.channels, self.img_size, self.img_size)
        x_input = torch.randn(self.batch_size, self.channels, self.img_size, self.img_size)
        
        # 移动到指定设备
        rgb_input = rgb_input.to(self.device)
        x_input = x_input.to(self.device)
        
        return {
            'RGB': rgb_input,
            'X': x_input
        }
    
    def test_input_router(self) -> bool:
        """
        测试InputRouter的功能
        
        Returns:
            bool: 测试是否通过
        """
        try:
            LOGGER.info("🧪 Testing InputRouter functionality...")
            
            # Create test data
            test_inputs = self.create_test_inputs()
            
            # Test RGB router
            rgb_router = InputRouter('RGB')
            rgb_router.to(self.device)
            
            # Test X router
            x_router = InputRouter('X')
            x_router.to(self.device)
            
            # Test dictionary input
            rgb_output_dict = rgb_router(test_inputs)
            x_output_dict = x_router(test_inputs)
            
            assert torch.equal(rgb_output_dict, test_inputs['RGB']), "RGB router dictionary input test failed"
            assert torch.equal(x_output_dict, test_inputs['X']), "X router dictionary input test failed"
            
            # Test tuple input
            tuple_inputs = (test_inputs['RGB'], test_inputs['X'])
            rgb_output_tuple = rgb_router(tuple_inputs)
            x_output_tuple = x_router(tuple_inputs)
            
            assert torch.equal(rgb_output_tuple, test_inputs['RGB']), "RGB router tuple input test failed"
            assert torch.equal(x_output_tuple, test_inputs['X']), "X router tuple input test failed"
            
            # Test list input
            list_inputs = [test_inputs['RGB'], test_inputs['X']]
            rgb_output_list = rgb_router(list_inputs)
            x_output_list = x_router(list_inputs)
            
            assert torch.equal(rgb_output_list, test_inputs['RGB']), "RGB router list input test failed"
            assert torch.equal(x_output_list, test_inputs['X']), "X router list input test failed"
            
            # Test single input (RGB only)
            rgb_single_output = rgb_router(test_inputs['RGB'])
            assert torch.equal(rgb_single_output, test_inputs['RGB']), "RGB router single input test failed"
            
            LOGGER.info("✅ InputRouter functionality test passed")
            return True
            
        except Exception as e:
            LOGGER.error(f"❌ InputRouter test failed: {e}")
            return False
    
    def test_model_forward(self) -> bool:
        """
        测试模型前向推理
        
        Returns:
            bool: 测试是否通过
        """
        try:
            LOGGER.info("🧪 Testing model forward inference...")
            
            if self.model is None:
                LOGGER.error("Model not loaded")
                return False
            
            # Create test data
            test_inputs = self.create_test_inputs()
            
            # Test different input formats
            input_formats = [
                ('Dictionary format', test_inputs),
                ('Tuple format', (test_inputs['RGB'], test_inputs['X'])),
                ('List format', [test_inputs['RGB'], test_inputs['X']])
            ]
            
            results = {}
            
            for format_name, inputs in input_formats:
                LOGGER.info(f"  Testing {format_name} input...")
                
                # Record inference time
                start_time = time.time()
                
                with torch.no_grad():
                    outputs = self.model(inputs)
                
                inference_time = time.time() - start_time
                
                # Validate output format
                if isinstance(outputs, tuple) and len(outputs) >= 2:
                    predictions, proto = outputs[0], outputs[1]
                    
                    LOGGER.info(f"    Prediction output shape: {predictions.shape}")
                    LOGGER.info(f"    Prototype output shape: {proto.shape}")
                    LOGGER.info(f"    Inference time: {inference_time:.4f}s")
                    
                    results[format_name] = {
                        'predictions_shape': predictions.shape,
                        'proto_shape': proto.shape,
                        'inference_time': inference_time
                    }
                else:
                    LOGGER.error(f"    Incorrect output format: {type(outputs)}")
                    return False
            
            # Verify output consistency across different input formats
            shapes_consistent = all(
                results['Dictionary format']['predictions_shape'] == result['predictions_shape'] and
                results['Dictionary format']['proto_shape'] == result['proto_shape']
                for result in results.values()
            )
            
            if shapes_consistent:
                LOGGER.info("✅ Model forward inference test passed")
                LOGGER.info("✅ Output consistency verification across input formats passed")
                return True
            else:
                LOGGER.error("❌ Output inconsistency across different input formats")
                return False
                
        except Exception as e:
            LOGGER.error(f"❌ Model forward inference test failed: {e}")
            return False
    
    def test_model_components(self) -> bool:
        """
        测试模型组件
        
        Returns:
            bool: 测试是否通过
        """
        try:
            LOGGER.info("🧪 Testing model components...")
            
            if self.model is None:
                LOGGER.error("Model not loaded")
                return False
            
            # Check if InputRouter is included
            has_input_router = any(isinstance(m, InputRouter) for m in self.model.modules())
            if has_input_router:
                LOGGER.info("✅ InputRouter component detected")
            else:
                LOGGER.warning("⚠️  InputRouter component not detected")
            
            # Check model structure
            total_params = sum(p.numel() for p in self.model.parameters())
            trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
            
            LOGGER.info(f"Total parameters: {total_params:,}")
            LOGGER.info(f"Trainable parameters: {trainable_params:,}")
            
            # Check device consistency
            model_device = next(self.model.parameters()).device
            if model_device == self.device:
                LOGGER.info(f"✅ Model device consistency check passed: {model_device}")
            else:
                LOGGER.error(f"❌ Model device inconsistency: expected {self.device}, actual {model_device}")
                return False
            
            return True
            
        except Exception as e:
            LOGGER.error(f"❌ Model component test failed: {e}")
            return False
    
    def run_all_tests(self) -> bool:
        """
        运行所有测试
        
        Returns:
            bool: 所有测试是否通过
        """
        LOGGER.info(f"\n{colorstr('bright_blue', 'bold', '='*60)}")
        LOGGER.info(f"{colorstr('bright_blue', 'bold', 'Starting Multimodal Model Tests')}")
        LOGGER.info(f"{colorstr('bright_blue', 'bold', '='*60)}")
        
        test_results = []
        
        # Test 1: Model Loading
        LOGGER.info(f"\n{colorstr('yellow', 'Test 1: Model Loading')}")
        test_results.append(self.load_model())
        
        if test_results[-1]:
            # Test 2: InputRouter Functionality
            LOGGER.info(f"\n{colorstr('yellow', 'Test 2: InputRouter Functionality')}")
            test_results.append(self.test_input_router())
            
            # Test 3: Model Components
            LOGGER.info(f"\n{colorstr('yellow', 'Test 3: Model Components')}")
            test_results.append(self.test_model_components())
            
            # Test 4: Model Forward Inference
            LOGGER.info(f"\n{colorstr('yellow', 'Test 4: Model Forward Inference')}")
            test_results.append(self.test_model_forward())
        
        # Summary of results
        LOGGER.info(f"\n{colorstr('bright_blue', 'bold', '='*60)}")
        LOGGER.info(f"{colorstr('bright_blue', 'bold', 'Test Results Summary')}")
        LOGGER.info(f"{colorstr('bright_blue', 'bold', '='*60)}")
        
        test_names = ['Model Loading', 'InputRouter Functionality', 'Model Components', 'Model Forward Inference']
        for i, (name, result) in enumerate(zip(test_names, test_results)):
            status = "✅ Passed" if result else "❌ Failed"
            LOGGER.info(f"{i+1}. {name}: {status}")
        
        all_passed = all(test_results)
        
        if all_passed:
            LOGGER.info(f"\n{colorstr('green', 'bold', '🎉 All tests passed! Multimodal model is working correctly.')}")
        else:
            LOGGER.info(f"\n{colorstr('red', 'bold', '❌ Some tests failed, please check the error messages above.')}")
        
        return all_passed


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Multimodal YOLOv5 model testing script')
    parser.add_argument('--config', type=str, default='models/segment/yolov5n-mid-seg.yaml',
                        help='Model configuration file path')
    parser.add_argument('--device', type=str, default='cpu',
                        help='Device to run on (cpu, cuda, auto)')
    parser.add_argument('--img-size', type=int, default=640,
                        help='Input image size')
    
    args = parser.parse_args()
    
    # Create tester
    tester = MultiModalModelTester(
        config_path=args.config,
        device=args.device
    )
    tester.img_size = args.img_size
    
    # Run tests
    success = tester.run_all_tests()
    
    # Exit code
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()