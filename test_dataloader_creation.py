import sys
sys.path.append('.')

from pathlib import Path
import yaml
from utils.segment.dataloaders import create_multimodal_dataloader

# 加载配置
with open('dataset-seg/datasets.yaml', 'r', encoding='utf-8') as f:
    data_dict = yaml.safe_load(f)

train_path = str(Path(data_dict['path']) / data_dict['train'])
train_xpl_path = str(Path(data_dict['path']) / data_dict['train_xpl'])

print('train_path:', train_path)
print('train_xpl_path:', train_xpl_path)

# 测试创建数据加载器
try:
    result = create_multimodal_dataloader(
        train_path,
        640,  # imgsz
        8,    # batch_size
        32,   # stride
        single_cls=False,
        hyp={'mosaic': 1.0, 'mixup': 0.0, 'copy_paste': 0.0, 'degrees': 0.0, 'translate': 0.1, 'scale': 0.5, 'shear': 0.0, 'perspective': 0.0, 'flipud': 0.0, 'fliplr': 0.5, 'hsv_h': 0.015, 'hsv_s': 0.7, 'hsv_v': 0.4},
        augment=True,
        cache=False,
        pad=0.0,
        rect=False,
        rank=-1,
        workers=0,  # 设为0避免多进程问题
        image_weights=False,
        quad=False,
        prefix="train: ",
        shuffle=True,
        mask_downsample_ratio=1,
        overlap_mask=False,
        seed=0,
        xpl_path=train_xpl_path,
        multimodal=True,
    )
    
    print('Result type:', type(result))
    print('Result length:', len(result))
    print('Result[0] type:', type(result[0]))
    print('Result[1] type:', type(result[1]))
    
    # 测试数据加载器
    train_loader, dataset = result
    print('Dataset type:', type(dataset))
    print('Dataset class name:', dataset.__class__.__name__)
    
    # 测试获取一个批次
    for i, batch_data in enumerate(train_loader):
        print('Batch data type:', type(batch_data))
        print('Batch data length:', len(batch_data))
        for j, item in enumerate(batch_data):
            print(f'  Item {j} type: {type(item)}, shape: {getattr(item, "shape", "N/A")}')
        break  # 只测试第一个批次
        
except Exception as e:
    print('Error:', e)
    import traceback
    traceback.print_exc()