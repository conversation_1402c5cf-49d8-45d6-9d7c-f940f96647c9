#!/usr/bin/env python3
"""
双模态数据增强一致性测试脚本

该脚本用于验证PPL和XPL图像在数据增强过程中的空间一致性。
测试包括：
1. 几何变换一致性（random_perspective）
2. HSV色彩空间增强一致性（augment_hsv）
3. 复制粘贴增强一致性（copy_paste）
4. MixUp增强一致性（mixup）
5. Albumentations增强一致性
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import sys
import os

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from utils.augmentations import (
    random_perspective, 
    augment_hsv, 
    copy_paste, 
    mixup, 
    Albumentations
)

def create_test_images():
    """创建测试用的PPL和XPL图像对"""
    # 创建一个简单的测试图像（640x640）
    h, w = 640, 640
    
    # PPL图像：彩色棋盘格
    ppl_img = np.zeros((h, w, 3), dtype=np.uint8)
    for i in range(0, h, 80):
        for j in range(0, w, 80):
            if (i//80 + j//80) % 2 == 0:
                ppl_img[i:i+80, j:j+80] = [255, 0, 0]  # 红色
            else:
                ppl_img[i:i+80, j:j+80] = [0, 255, 0]  # 绿色
    
    # XPL图像：灰度条纹
    xpl_img = np.zeros((h, w, 3), dtype=np.uint8)
    for i in range(0, h, 40):
        if (i//40) % 2 == 0:
            xpl_img[i:i+40, :] = [128, 128, 128]  # 灰色
        else:
            xpl_img[i:i+40, :] = [200, 200, 200]  # 浅灰色
    
    return ppl_img, xpl_img

def create_test_labels_segments():
    """创建测试用的标签和分割段"""
    # 简单的测试标签 [class, x_center, y_center, width, height]
    labels = np.array([
        [0, 0.3, 0.3, 0.2, 0.2],  # 第一个目标
        [1, 0.7, 0.7, 0.15, 0.15]  # 第二个目标
    ])
    
    # 简单的分割段（矩形）
    segments = [
        np.array([[0.2, 0.2], [0.4, 0.2], [0.4, 0.4], [0.2, 0.4]]) * 640,  # 第一个分割段
        np.array([[0.625, 0.625], [0.775, 0.625], [0.775, 0.775], [0.625, 0.775]]) * 640  # 第二个分割段
    ]
    
    return labels, segments

def test_random_perspective():
    """测试random_perspective函数的双模态一致性"""
    print("测试random_perspective函数...")
    
    ppl_img, xpl_img = create_test_images()
    labels, segments = create_test_labels_segments()
    
    # 应用random_perspective增强
    result = random_perspective(
        ppl_img.copy(), 
        targets=labels.copy(), 
        segments=segments.copy(),
        degrees=10,
        translate=0.1,
        scale=0.1,
        shear=5,
        perspective=0.0,
        im_xpl=xpl_img.copy()
    )
    
    if len(result) == 4:  # 双模态返回
        aug_ppl, aug_labels, aug_segments, aug_xpl = result
        print("✓ random_perspective双模态增强成功")
        return aug_ppl, aug_xpl, "random_perspective"
    else:
        print("✗ random_perspective双模态增强失败")
        return None, None, None

def test_augment_hsv():
    """测试augment_hsv函数的双模态一致性"""
    print("测试augment_hsv函数...")
    
    ppl_img, xpl_img = create_test_images()
    
    # 应用HSV增强
    aug_ppl = augment_hsv(ppl_img.copy(), hgain=0.5, sgain=0.5, vgain=0.5, im_xpl=xpl_img.copy())
    
    if isinstance(aug_ppl, tuple) and len(aug_ppl) == 2:
        aug_ppl, aug_xpl = aug_ppl
        print("✓ augment_hsv双模态增强成功")
        return aug_ppl, aug_xpl, "augment_hsv"
    else:
        print("✗ augment_hsv双模态增强失败")
        return None, None, None

def test_copy_paste():
    """测试copy_paste函数的双模态一致性"""
    print("测试copy_paste函数...")
    
    ppl_img, xpl_img = create_test_images()
    labels, segments = create_test_labels_segments()
    
    # 应用copy_paste增强
    result = copy_paste(
        ppl_img.copy(), 
        labels.copy(), 
        segments.copy(),
        p=0.5,
        im_xpl=xpl_img.copy()
    )
    
    if len(result) == 4:  # 双模态返回
        aug_ppl, aug_labels, aug_segments, aug_xpl = result
        print("✓ copy_paste双模态增强成功")
        return aug_ppl, aug_xpl, "copy_paste"
    else:
        print("✗ copy_paste双模态增强失败")
        return None, None, None

def test_mixup():
    """测试mixup函数的双模态一致性"""
    print("测试mixup函数...")
    
    ppl_img1, xpl_img1 = create_test_images()
    ppl_img2, xpl_img2 = create_test_images()
    
    # 为第二组图像添加一些变化
    ppl_img2 = cv2.flip(ppl_img2, 1)
    xpl_img2 = cv2.flip(xpl_img2, 1)
    
    labels1, _ = create_test_labels_segments()
    labels2 = labels1.copy()
    
    # 应用mixup增强
    result = mixup(
        ppl_img1, labels1, 
        ppl_img2, labels2,
        im_xpl=xpl_img1,
        im2_xpl=xpl_img2
    )
    
    if len(result) == 3:  # 双模态返回
        aug_ppl, aug_labels, aug_xpl = result
        print("✓ mixup双模态增强成功")
        return aug_ppl, aug_xpl, "mixup"
    else:
        print("✗ mixup双模态增强失败")
        return None, None, None

def test_albumentations():
    """测试Albumentations类的双模态一致性"""
    print("测试Albumentations类...")
    
    try:
        ppl_img, xpl_img = create_test_images()
        labels, _ = create_test_labels_segments()
        
        # 创建Albumentations实例
        transform = Albumentations()
        
        # 应用Albumentations增强
        result = transform(ppl_img.copy(), labels.copy(), p=1.0, im_xpl=xpl_img.copy())
        
        if len(result) == 3:  # 双模态返回
            aug_ppl, aug_labels, aug_xpl = result
            print("✓ Albumentations双模态增强成功")
            return aug_ppl, aug_xpl, "albumentations"
        else:
            print("✗ Albumentations双模态增强失败")
            return None, None, None
    except Exception as e:
        print(f"✗ Albumentations测试失败: {e}")
        return None, None, None

def visualize_results(original_ppl, original_xpl, results):
    """可视化测试结果"""
    print("生成可视化结果...")
    
    # 创建输出目录
    output_dir = Path("test_results")
    output_dir.mkdir(exist_ok=True)
    
    # 保存原始图像
    cv2.imwrite(str(output_dir / "original_ppl.jpg"), cv2.cvtColor(original_ppl, cv2.COLOR_RGB2BGR))
    cv2.imwrite(str(output_dir / "original_xpl.jpg"), cv2.cvtColor(original_xpl, cv2.COLOR_RGB2BGR))
    
    # 保存增强后的图像
    for aug_ppl, aug_xpl, method_name in results:
        if aug_ppl is not None and aug_xpl is not None:
            cv2.imwrite(str(output_dir / f"{method_name}_ppl.jpg"), cv2.cvtColor(aug_ppl, cv2.COLOR_RGB2BGR))
            cv2.imwrite(str(output_dir / f"{method_name}_xpl.jpg"), cv2.cvtColor(aug_xpl, cv2.COLOR_RGB2BGR))
    
    print(f"结果已保存到 {output_dir} 目录")

def calculate_consistency_metrics(ppl_img, xpl_img, aug_ppl, aug_xpl):
    """计算一致性指标"""
    # 计算结构相似性（简化版）
    # 这里使用简单的像素差异作为一致性指标
    
    # 将图像转换为灰度
    ppl_gray = cv2.cvtColor(ppl_img, cv2.COLOR_RGB2GRAY)
    xpl_gray = cv2.cvtColor(xpl_img, cv2.COLOR_RGB2GRAY)
    aug_ppl_gray = cv2.cvtColor(aug_ppl, cv2.COLOR_RGB2GRAY)
    aug_xpl_gray = cv2.cvtColor(aug_xpl, cv2.COLOR_RGB2GRAY)
    
    # 计算变化程度
    ppl_change = np.mean(np.abs(ppl_gray.astype(float) - aug_ppl_gray.astype(float)))
    xpl_change = np.mean(np.abs(xpl_gray.astype(float) - aug_xpl_gray.astype(float)))
    
    # 一致性分数（变化程度的相似性）
    consistency_score = 1.0 - abs(ppl_change - xpl_change) / max(ppl_change, xpl_change, 1.0)
    
    return consistency_score, ppl_change, xpl_change

def main():
    """主测试函数"""
    print("开始双模态数据增强一致性测试...")
    print("=" * 50)
    
    # 创建原始测试图像
    original_ppl, original_xpl = create_test_images()
    
    # 运行所有测试
    test_functions = [
        test_random_perspective,
        test_augment_hsv,
        test_copy_paste,
        test_mixup,
        test_albumentations
    ]
    
    results = []
    consistency_scores = []
    
    for test_func in test_functions:
        aug_ppl, aug_xpl, method_name = test_func()
        if aug_ppl is not None and aug_xpl is not None:
            results.append((aug_ppl, aug_xpl, method_name))
            
            # 计算一致性指标
            consistency_score, ppl_change, xpl_change = calculate_consistency_metrics(
                original_ppl, original_xpl, aug_ppl, aug_xpl
            )
            consistency_scores.append((method_name, consistency_score, ppl_change, xpl_change))
    
    # 可视化结果
    visualize_results(original_ppl, original_xpl, results)
    
    # 输出一致性报告
    print("\n" + "=" * 50)
    print("一致性测试报告:")
    print("=" * 50)
    
    for method_name, consistency_score, ppl_change, xpl_change in consistency_scores:
        print(f"{method_name:20s}: 一致性分数 = {consistency_score:.3f}, "
              f"PPL变化 = {ppl_change:.2f}, XPL变化 = {xpl_change:.2f}")
    
    if consistency_scores:
        avg_consistency = np.mean([score for _, score, _, _ in consistency_scores])
        print(f"\n平均一致性分数: {avg_consistency:.3f}")
        
        if avg_consistency > 0.8:
            print("✓ 双模态增强一致性测试通过！")
        else:
            print("⚠ 双模态增强一致性需要改进")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()