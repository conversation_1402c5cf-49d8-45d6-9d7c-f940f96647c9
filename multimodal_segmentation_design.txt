YOLOv5岩心薄片多模态分割模型技术修改方案
==========================================

项目目标：
基于现有YOLOv5分割框架，实现岩心薄片的双模态分割模型(yolov5n-mid-seg)
- RGB模态：单偏光图像
- X模态：正交光图像
- 融合策略：中期融合

技术修改线路：

1. 模型架构修改 (models/)
   1.1 优化模型加载方式 - 基于InputRouter的统一输入处理
       - 使用现有的InputRouter类作为多模态输入统一接口
       - 支持字典、元组、列表等多种输入格式的自动路由
       - 简化模型初始化：通过配置文件自动识别多模态结构
       - 保持向后兼容性：单模态模型无需修改即可使用
   
   1.2 简化 models/yolo.py 的多模态支持
       - 利用现有SegmentationModel架构，最小化代码修改
       - InputRouter自动处理多模态输入路由('RGB', 'X')
       - 保持原有parse_model函数结构，仅扩展InputRouter解析
       - 统一前向传播接口：model(inputs) 支持任意输入格式
       - 自动检测输入类型并路由到对应的模态分支
   
   1.3 配置驱动的模型加载 yolov5n-mid-seg.yaml
       - 配置文件中明确定义InputRouter层及其目标模态
       - 自动推断双模态输入通道配置(RGB: 3通道, X: 3通道)
       - P3和P4层级融合点通过配置文件灵活定义
       - 支持运行时模态切换：单模态/双模态自适应

datasets.yaml配置示例：
```yaml
# 岩心薄片双模态分割数据集配置
train: dataset-seg/images/train
val: dataset-seg/images/val

# 双模态路径配置
train_xpl: dataset-seg/images_xpl/train
val_xpl: dataset-seg/images_xpl/val

# 类别数和类别名称
nc: 5  # 岩心薄片矿物类别数
names: ['quartz', 'feldspar', 'mica', 'calcite', 'others']

# 多模态开关（简化配置）
multimodal: true  # true启用双模态，false使用单模态
```

2. 数据加载修改 (utils/segment/)
   2.1 修改 utils/segment/dataloaders.py
       - 扩展 LoadImagesAndLabelsAndMasks 类为 LoadDualModalImagesAndMasks
       - 实现基于文件名的双模态图像配对加载机制
       - 支持岩心薄片数据集的特定文件结构：
         * images/ : 单偏光图像(PPL) - RGB模态
         * images_xpl/ : 正交光图像(XPL) - X模态
         * labels/ : 分割标注文件
       - 修改 collate_fn 函数处理双模态批次数据
       - 添加模态数据路径验证和配对完整性检查
       - 支持模态缺失时的降级处理
   
   2.2 修改 utils/segment/augmentations.py
       - 扩展数据增强函数支持双模态同步变换
       - 确保PPL和XPL图像增强的空间一致性
       - 针对岩心薄片的偏光特性优化增强策略
       

3. 损失函数修改 (utils/segment/)/无需修改
   3.1 修改 utils/segment/loss.py
       - 扩展 ComputeLoss 类支持双模态融合特征
       - 保持原有分割损失函数结构

4. 训练脚本修改 (segment/)
   4.1 修改 segment/train.py
       - 适配双模态数据加载器
       - 修改训练循环支持双模态输入
       - 添加岩心薄片特定的验证逻辑
       - 更新模型保存和加载机制
       - 支持单模态和双模态模式切换
   
   4.2 修改 segment/val.py
       - 适配双模态验证流程
       - 保持现有评估指标计算
       - 添加模态缺失情况的处理-复制一张第一个模态的图像送入骨干网络

5. 通用工具修改 (utils/)
   5.1 修改 utils/dataloaders.py
       - 创建独立的多模态数据加载器 (MultiModalDataLoader)
       - 避免修改现有单模态加载器，防止数据冗余和兼容性问题
       - 实现专用的双模态数据加载逻辑
       - 添加岩心薄片数据集的文件路径处理
       - 支持双模态缓存机制
       - 添加模态数据完整性检查
       - 提供多模态数据加载器工厂函数

   5.2 多模态数据加载器架构设计
       - MultiModalDataLoader: 主要数据加载器类
         * 继承自torch.utils.data.DataLoader
         * 专门处理双模态数据批次生成
         * 内置数据完整性验证
       - MultiModalDataset: 数据集类
         * 继承自torch.utils.data.Dataset
         * 管理PPL和XPL图像的配对加载
         * 实现双模态数据缓存策略
       - create_multimodal_dataloader(): 工厂函数
          * 根据配置自动选择单模态或多模态加载器
          * 提供统一的数据加载接口
          * 支持向后兼容性
          * 简化配置：仅通过multimodal字段判断
          * 使用datasets.yaml中的train和train_xpl路径

 

岩心薄片数据格式要求：
- RGB模态：单偏光图像（PPL，标准RGB格式）
- X模态：正交光图像（XPL，可为RGB）
- 标注格式：保持YOLOv5分割标注兼容性
- 文件结构：支持配对的双模态图像文件

详细数据加载设计方案：

数据集文件结构：
```
dataset-seg/
├── datasets.yaml          # 数据集配置文件
├── images/                # PPL单偏光图像
│   ├── train/
│   │   ├── tile_0019.png
│   │   ├── tile_0020.png
│   │   └── ...
│   └── val/
│       ├── tile_0022.png
│       └── ...
├── images_xpl/            # XPL正交光图像
│   ├── train/
│   │   ├── tile_0019.png  # 与PPL同名配对
│   │   ├── tile_0020.png
│   │   └── ...
│   └── val/
│       ├── tile_0022.png
│       └── ...
└── labels/                # 分割标注
    ├── train/
    │   ├── tile_0019.txt
    │   └── ...
    └── val/
        ├── tile_0022.txt
        └── ...
```

双模态数据加载机制：
1. 文件名配对策略：
   - 基于相同的文件名（如tile_0019.png）在images/和images_xpl/中查找配对图像
   - 支持不同扩展名的配对（.png, .jpg等）
   - 建立文件名到路径的映射表，确保配对完整性

2. LoadDualModalImagesAndMasks类设计：
   - 继承自LoadImagesAndLabelsAndMasks
   - 新增属性：
     * self.ppl_files: PPL图像文件路径列表
     * self.xpl_files: XPL图像文件路径列表
     * self.modal_pairs: 配对映射字典
   - 重写__init__方法：解析双模态文件结构
   - 重写load_image方法：同时加载PPL和XPL图像

3. 数据加载流程：
   - 扫描images/目录获取PPL图像列表
   - 根据文件名在images_xpl/中查找对应XPL图像
   - 验证配对完整性，记录缺失的模态文件
   - 构建双模态数据索引

4. __getitem__方法修改：
   - 同时加载PPL和XPL图像
   - 确保两个模态图像的尺寸一致性
   - 应用相同的几何变换（旋转、缩放、裁剪）
   - 返回格式：(ppl_img, xpl_img, labels, path, shapes, masks)

5. collate_fn函数修改：
   - 处理双模态批次数据
   - 分别堆叠PPL和XPL图像张量
   - 返回格式：(ppl_batch, xpl_batch, labels_batch, paths, shapes, masks)

6. 错误处理和降级策略：
   - 当XPL图像缺失时，使用PPL图像复制或零填充
   - 当PPL图像缺失时，跳过该样本或使用XPL图像
   - 记录模态缺失统计信息

7. 缓存机制扩展：
   - 支持双模态图像的缓存
   - 缓存文件命名：train.cache, train_xpl.cache
   - 验证缓存完整性和版本兼容性



文件结构建议：
```
models/
├── multimodal.py          # 新增：独立的多模态模块
├── yolo.py               # 修改：支持双模态解析
└── segment/
    └── yolov5n-mid-seg.yaml  # 完善：双模态配置

utils/segment/
├── dataloaders.py        # 修改：双模态数据加载
├── augmentations.py      # 修改：同步数据增强
└── loss.py              # 修改：适配融合特征

segment/
├── train.py             # 修改：双模态训练
└── val.py               # 修改：双模态验证
```