#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Dual-Modal Dataloader Test Script

This script tests the LoadDualModalImagesAndMasks class and create_multimodal_dataloader function
to verify dual-modal data loading functionality for YOLOv5 multimodal segmentation.

Author: AI Assistant
Date: 2024
"""

import os
import sys
import tempfile
from pathlib import Path
import numpy as np
import cv2
import torch
from PIL import Image

# Add YOLOv5 root to path
sys.path.append(str(Path(__file__).parent))

from utils.segment.dataloaders import LoadDualModalImagesAndMasks, create_multimodal_dataloader
from utils.general import LOGGER


class DualModalDataloaderTester:
    """Test class for dual-modal dataloader functionality."""
    
    def __init__(self):
        self.test_dir = None
        self.ppl_dir = None
        self.xpl_dir = None
        self.labels_dir = None
        
    def setup_test_data(self):
        """Create temporary test data structure."""
        print("Setting up test data...")
        
        # Create temporary directory structure
        self.test_dir = Path(tempfile.mkdtemp(prefix="dual_modal_test_"))
        self.ppl_dir = self.test_dir / "images" / "train"
        self.xpl_dir = self.test_dir / "images_xpl" / "train"
        self.labels_dir = self.test_dir / "labels" / "train"
        
        # Create directories
        self.ppl_dir.mkdir(parents=True, exist_ok=True)
        self.xpl_dir.mkdir(parents=True, exist_ok=True)
        self.labels_dir.mkdir(parents=True, exist_ok=True)
        
        # Create test images and labels
        test_files = ["tile_0001", "tile_0002", "tile_0003"]
        
        for filename in test_files:
            # Create PPL image (RGB)
            ppl_img = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
            ppl_path = self.ppl_dir / f"{filename}.png"
            cv2.imwrite(str(ppl_path), ppl_img)
            
            # Create XPL image (different from PPL)
            xpl_img = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
            xpl_path = self.xpl_dir / f"{filename}.png"
            cv2.imwrite(str(xpl_path), xpl_img)
            
            # Create dummy label file
            label_path = self.labels_dir / f"{filename}.txt"
            with open(label_path, 'w') as f:
                # Class 0, normalized bbox coordinates, and dummy segmentation points
                f.write("0 0.5 0.5 0.3 0.3 0.4 0.4 0.6 0.4 0.6 0.6 0.4 0.6\n")
                
        # Create one missing XPL file to test fallback
        missing_file = "tile_0004"
        ppl_img = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        ppl_path = self.ppl_dir / f"{missing_file}.png"
        cv2.imwrite(str(ppl_path), ppl_img)
        
        label_path = self.labels_dir / f"{missing_file}.txt"
        with open(label_path, 'w') as f:
            f.write("0 0.5 0.5 0.2 0.2 0.3 0.3 0.7 0.3 0.7 0.7 0.3 0.7\n")
            
        print(f"Test data created in: {self.test_dir}")
        print(f"PPL images: {len(list(self.ppl_dir.glob('*.png')))}")
        print(f"XPL images: {len(list(self.xpl_dir.glob('*.png')))}")
        print(f"Label files: {len(list(self.labels_dir.glob('*.txt')))}")
        
    def test_dual_modal_dataset(self):
        """Test LoadDualModalImagesAndMasks class."""
        print("\n=== Testing LoadDualModalImagesAndMasks ===")
        
        try:
            # Create dataset
            dataset = LoadDualModalImagesAndMasks(
                path=str(self.ppl_dir),
                img_size=640,
                batch_size=2,
                augment=False,
                hyp={'mosaic': 0.0, 'mixup': 0.0, 'hsv_h': 0.0, 'hsv_s': 0.0, 'hsv_v': 0.0, 
                     'flipud': 0.0, 'fliplr': 0.0, 'degrees': 0.0, 'translate': 0.0, 
                     'scale': 0.0, 'shear': 0.0, 'perspective': 0.0, 'copy_paste': 0.0},
                rect=False,
                cache_images=False,
                single_cls=False,
                stride=32,
                pad=0.0,
                prefix="TEST: ",
                downsample_ratio=1,
                overlap=False,
                rank=-1,
                xpl_path=str(self.xpl_dir),
            )
            
            print(f"Dataset initialized successfully")
            print(f"Total samples: {len(dataset)}")
            print(f"PPL files: {len(dataset.ppl_files)}")
            print(f"XPL files: {len(dataset.xpl_files)}")
            print(f"Missing XPL files: {len(dataset.missing_xpl)}")
            
            # Test data loading
            print("\nTesting data loading...")
            for i in range(min(3, len(dataset))):
                sample = dataset[i]
                ppl_img, xpl_img, labels, path, shapes, masks = sample
                
                print(f"Sample {i}:")
                print(f"  PPL image shape: {ppl_img.shape}")
                print(f"  XPL image shape: {xpl_img.shape}")
                print(f"  Labels shape: {labels.shape}")
                print(f"  Masks shape: {masks.shape}")
                print(f"  Path: {Path(path).name}")
                print(f"  Shapes: {shapes}")
                
                # Verify tensor types
                assert isinstance(ppl_img, torch.Tensor), "PPL image should be torch.Tensor"
                assert isinstance(xpl_img, torch.Tensor), "XPL image should be torch.Tensor"
                assert isinstance(labels, torch.Tensor), "Labels should be torch.Tensor"
                assert isinstance(masks, torch.Tensor), "Masks should be torch.Tensor"
                
                # Verify shapes
                assert ppl_img.shape == xpl_img.shape, "PPL and XPL images should have same shape"
                assert ppl_img.shape[0] == 3, "Images should have 3 channels"
                
            print("✓ LoadDualModalImagesAndMasks test passed!")
            return True
            
        except Exception as e:
            print(f"✗ LoadDualModalImagesAndMasks test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    def test_multimodal_dataloader(self):
        """Test create_multimodal_dataloader function."""
        print("\n=== Testing create_multimodal_dataloader ===")
        
        try:
            # Test multimodal dataloader
            print("Testing multimodal dataloader...")
            dataloader, dataset = create_multimodal_dataloader(
                path=str(self.ppl_dir),
                imgsz=640,
                batch_size=2,
                stride=32,
                single_cls=False,
                hyp={'mosaic': 0.0, 'mixup': 0.0, 'hsv_h': 0.0, 'hsv_s': 0.0, 'hsv_v': 0.0, 
                     'flipud': 0.0, 'fliplr': 0.0, 'degrees': 0.0, 'translate': 0.0, 
                     'scale': 0.0, 'shear': 0.0, 'perspective': 0.0, 'copy_paste': 0.0},
                augment=False,
                cache=False,
                pad=0.0,
                rect=False,
                rank=-1,
                workers=0,  # Use 0 workers for testing
                image_weights=False,
                quad=False,
                prefix="MULTIMODAL TEST: ",
                shuffle=False,
                mask_downsample_ratio=1,
                overlap_mask=False,
                seed=0,
                xpl_path=str(self.xpl_dir),
                multimodal=True,
            )
            
            print(f"Multimodal dataloader created successfully")
            print(f"Dataset type: {type(dataset).__name__}")
            print(f"Dataloader type: {type(dataloader).__name__}")
            
            # Test batch loading
            print("\nTesting batch loading...")
            batch_count = 0
            for batch in dataloader:
                ppl_batch, xpl_batch, labels_batch, paths, shapes, masks_batch = batch
                
                print(f"Batch {batch_count}:")
                print(f"  PPL batch shape: {ppl_batch.shape}")
                print(f"  XPL batch shape: {xpl_batch.shape}")
                print(f"  Labels batch shape: {labels_batch.shape}")
                print(f"  Masks batch shape: {masks_batch.shape}")
                print(f"  Batch size: {len(paths)}")
                
                # Verify batch dimensions
                assert ppl_batch.shape == xpl_batch.shape, "PPL and XPL batches should have same shape"
                assert ppl_batch.shape[0] <= 2, "Batch size should not exceed 2"
                assert ppl_batch.shape[1] == 3, "Images should have 3 channels"
                
                batch_count += 1
                if batch_count >= 2:  # Test first 2 batches
                    break
                    
            print("✓ create_multimodal_dataloader test passed!")
            
            # Test single-modal fallback
            print("\nTesting single-modal fallback...")
            dataloader_single, dataset_single = create_multimodal_dataloader(
                path=str(self.ppl_dir),
                imgsz=640,
                batch_size=2,
                stride=32,
                multimodal=False,  # Single modal
                workers=0,
                prefix="SINGLE MODAL TEST: ",
            )
            
            print(f"Single-modal dataset type: {type(dataset_single).__name__}")
            assert type(dataset_single).__name__ == "LoadImagesAndLabelsAndMasks", "Should use single-modal dataset"
            print("✓ Single-modal fallback test passed!")
            
            return True
            
        except Exception as e:
            print(f"✗ create_multimodal_dataloader test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    def test_file_pairing(self):
        """Test file pairing mechanism."""
        print("\n=== Testing File Pairing Mechanism ===")
        
        try:
            dataset = LoadDualModalImagesAndMasks(
                path=str(self.ppl_dir),
                img_size=640,
                batch_size=2,
                xpl_path=str(self.xpl_dir),
                prefix="PAIRING TEST: ",
            )
            
            print(f"File pairing results:")
            print(f"  Total PPL files: {len(dataset.ppl_files)}")
            print(f"  Total XPL files: {len(dataset.xpl_files)}")
            print(f"  Modal pairs: {len(dataset.modal_pairs)}")
            print(f"  Missing XPL files: {dataset.missing_xpl}")
            
            # Verify pairing
            for ppl_file in dataset.ppl_files[:3]:  # Check first 3 files
                ppl_stem = Path(ppl_file).stem
                xpl_file = dataset.modal_pairs[ppl_file]
                xpl_stem = Path(xpl_file).stem
                
                print(f"  {ppl_stem} -> {xpl_stem}")
                
                if ppl_stem in dataset.missing_xpl:
                    assert ppl_file == xpl_file, "Missing XPL should fallback to PPL"
                    print(f"    (Fallback: XPL missing, using PPL)")
                else:
                    assert ppl_stem == xpl_stem, "PPL and XPL should have same stem"
                    print(f"    (Paired successfully)")
                    
            print("✓ File pairing test passed!")
            return True
            
        except Exception as e:
            print(f"✗ File pairing test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    def cleanup(self):
        """Clean up test data."""
        if self.test_dir and self.test_dir.exists():
            import shutil
            shutil.rmtree(self.test_dir)
            print(f"\nCleaned up test data: {self.test_dir}")
            
    def run_all_tests(self):
        """Run all tests."""
        print("Starting Dual-Modal Dataloader Tests...")
        print("=" * 50)
        
        try:
            # Setup
            self.setup_test_data()
            
            # Run tests
            test_results = []
            test_results.append(self.test_file_pairing())
            test_results.append(self.test_dual_modal_dataset())
            test_results.append(self.test_multimodal_dataloader())
            
            # Summary
            print("\n" + "=" * 50)
            print("TEST SUMMARY")
            print("=" * 50)
            
            test_names = ["File Pairing", "Dual Modal Dataset", "Multimodal Dataloader"]
            for i, (name, result) in enumerate(zip(test_names, test_results)):
                status = "✓ PASSED" if result else "✗ FAILED"
                print(f"{i+1}. {name}: {status}")
                
            all_passed = all(test_results)
            print(f"\nOverall Result: {'✓ ALL TESTS PASSED' if all_passed else '✗ SOME TESTS FAILED'}")
            
            if all_passed:
                print("\n🎉 Dual-modal dataloader is working correctly!")
                print("The LoadDualModalImagesAndMasks class and create_multimodal_dataloader function")
                print("are ready for use in multimodal YOLOv5 training and inference.")
            else:
                print("\n❌ Some tests failed. Please check the implementation.")
                
            return all_passed
            
        except Exception as e:
            print(f"\n❌ Test execution failed: {e}")
            import traceback
            traceback.print_exc()
            return False
            
        finally:
            self.cleanup()


def main():
    """Main test function."""
    tester = DualModalDataloaderTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n" + "=" * 60)
        print("DUAL-MODAL DATALOADER VERIFICATION COMPLETE")
        print("=" * 60)
        print("✓ All tests passed successfully!")
        print("✓ Dual-modal data loading is working correctly.")
        print("✓ File pairing mechanism is functioning properly.")
        print("✓ Batch processing handles dual-modal data correctly.")
        print("✓ Error handling and fallback strategies are working.")
        print("\nThe dual-modal dataloader is ready for integration with")
        print("the multimodal YOLOv5 segmentation model.")
        return 0
    else:
        print("\n❌ Tests failed. Please review the implementation.")
        return 1


if __name__ == "__main__":
    exit(main())