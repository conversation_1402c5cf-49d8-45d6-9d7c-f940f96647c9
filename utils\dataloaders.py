# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license
"""数据加载器和数据集工具模块。

本模块包含了YOLOv5项目中用于数据加载和处理的核心功能，包括：
- 图像和视频数据加载器
- 数据集缓存和预处理
- 多种数据源支持（本地文件、网络流、屏幕截图等）
- 数据增强和变换
- 分布式训练支持
"""

# 标准库导入
import contextlib  # 上下文管理工具
import glob  # 文件路径模式匹配
import hashlib  # 哈希算法
import json  # JSON数据处理
import math  # 数学函数
import os  # 操作系统接口
import random  # 随机数生成
import shutil  # 高级文件操作
import time  # 时间相关功能
from itertools import repeat  # 迭代器工具
from multiprocessing.pool import Pool, ThreadPool  # 多进程和线程池
from pathlib import Path  # 面向对象的文件系统路径
from threading import Thread  # 线程支持
from urllib.parse import urlparse  # URL解析

# 第三方库导入
import numpy as np  # 数值计算
import psutil  # 系统和进程工具
import torch  # PyTorch深度学习框架
import torch.nn.functional as F  # PyTorch神经网络函数
import torchvision  # 计算机视觉工具
import yaml  # YAML文件处理
from PIL import ExifTags, Image, ImageOps  # 图像处理
from torch.utils.data import DataLoader, Dataset, dataloader, distributed  # PyTorch数据加载
from tqdm import tqdm  # 进度条显示

# 项目内部模块导入
from utils.augmentations import (  # 数据增强工具
    Albumentations,  # Albumentations数据增强库
    augment_hsv,  # HSV色彩空间增强
    copy_paste,  # 复制粘贴增强
    letterbox,  # 信箱填充（保持宽高比的缩放）
    mixup,  # MixUp数据增强
    random_perspective,  # 随机透视变换
)
from utils.general import (  # 通用工具函数
    DATASETS_DIR,  # 数据集目录
    LOGGER,  # 日志记录器
    NUM_THREADS,  # 线程数量
    TQDM_BAR_FORMAT,  # 进度条格式
    check_dataset,  # 检查数据集
    check_requirements,  # 检查依赖要求
    check_yaml,  # 检查YAML文件
    clean_str,  # 清理字符串
    cv2,  # OpenCV库
    is_colab,  # 检查是否在Colab环境
    is_kaggle,  # 检查是否在Kaggle环境
    segments2boxes,  # 分割掩码转边界框
    unzip_file,  # 解压文件
    xyn2xy,  # 归一化坐标转绝对坐标
    xywh2xyxy,  # 中心点格式转左上右下格式
    xywhn2xyxy,  # 归一化中心点格式转绝对坐标
    xyxy2xywhn,  # 绝对坐标转归一化中心点格式
)
from utils.torch_utils import torch_distributed_zero_first  # 分布式训练工具

# 全局参数配置
HELP_URL = "See https://docs.ultralytics.com/yolov5/tutorials/train_custom_data"  # 帮助文档链接
IMG_FORMATS = "bmp", "dng", "jpeg", "jpg", "mpo", "png", "tif", "tiff", "webp", "pfm"  # 支持的图像格式
LOCAL_RANK = int(os.getenv("LOCAL_RANK", -1))  # 本地进程排名（分布式训练）
RANK = int(os.getenv("RANK", -1))  # 全局进程排名（分布式训练）
WORLD_SIZE = int(os.getenv("WORLD_SIZE", 1))  # 总进程数（分布式训练）
PIN_MEMORY = str(os.getenv("PIN_MEMORY", True)).lower() == "true"  # 数据加载器的内存固定设置

# 获取图像方向EXIF标签
for orientation in ExifTags.TAGS.keys():
    if ExifTags.TAGS[orientation] == "Orientation":
        break


def get_hash(paths):
    """为文件或目录路径列表生成单个SHA256哈希值。
    
    通过组合文件大小和路径来生成唯一的哈希标识，用于缓存验证。
    
    Args:
        paths: 文件或目录路径列表
        
    Returns:
        str: SHA256哈希值的十六进制字符串
    """
    size = sum(os.path.getsize(p) for p in paths if os.path.exists(p))  # 计算所有文件的总大小
    h = hashlib.sha256(str(size).encode())  # 对大小进行哈希
    h.update("".join(paths).encode())  # 对路径进行哈希
    return h.hexdigest()  # 返回十六进制哈希值


def exif_size(img):
    """返回考虑EXIF方向信息的PIL图像尺寸。
    
    根据图像的EXIF方向标签调整图像尺寸，确保返回正确的宽高。
    
    Args:
        img: PIL图像对象
        
    Returns:
        tuple: 修正后的图像尺寸(width, height)
    """
    s = img.size  # 获取原始尺寸(width, height)
    with contextlib.suppress(Exception):
        rotation = dict(img._getexif().items())[orientation]
        if rotation in [6, 8]:  # 旋转270度或90度时需要交换宽高
            s = (s[1], s[0])
    return s


def exif_transpose(image):
    """根据EXIF方向标签转置PIL图像。
    
    这是PIL ImageOps.exif_transpose()的就地版本，用于根据图像的EXIF方向信息
    自动旋转图像到正确的方向。
    
    Args:
        image: 要转置的PIL图像对象
        
    Returns:
        Image: 转置后的图像对象
    """
    exif = image.getexif()  # 获取EXIF数据
    orientation = exif.get(0x0112, 1)  # 获取方向标签，默认为1（正常）
    if orientation > 1:
        # 根据EXIF方向标签选择相应的变换方法
        method = {
            2: Image.FLIP_LEFT_RIGHT,    # 水平翻转
            3: Image.ROTATE_180,         # 旋转180度
            4: Image.FLIP_TOP_BOTTOM,    # 垂直翻转
            5: Image.TRANSPOSE,          # 转置
            6: Image.ROTATE_270,         # 旋转270度
            7: Image.TRANSVERSE,         # 反转置
            8: Image.ROTATE_90,          # 旋转90度
        }.get(orientation)
        if method is not None:
            image = image.transpose(method)  # 应用变换
            del exif[0x0112]  # 删除方向标签
            image.info["exif"] = exif.tobytes()  # 更新EXIF信息
    return image


def seed_worker(worker_id):
    """为数据加载器工作进程设置随机种子以确保可重现性。
    
    基于PyTorch的随机性说明设置工作进程的随机种子，确保训练的可重现性。
    参考：https://pytorch.org/docs/stable/notes/randomness.html#dataloader
    
    Args:
        worker_id: 工作进程ID
    """
    worker_seed = torch.initial_seed() % 2**32  # 获取工作进程种子
    np.random.seed(worker_seed)  # 设置NumPy随机种子
    random.seed(worker_seed)  # 设置Python随机种子


class SmartDistributedSampler(distributed.DistributedSampler):
    """智能分布式采样器，确保跨GPU的确定性洗牌和平衡数据分布。
    
    这个采样器继承自PyTorch的DistributedSampler，提供了更智能的数据分布策略，
    确保在分布式训练中每个GPU获得平衡且确定性的数据样本。
    """

    def __iter__(self):
        """生成分布式数据采样的索引，基于epoch和seed进行确定性洗牌。
        
        Returns:
            Iterator: 数据索引的迭代器
        """
        g = torch.Generator()  # 创建随机数生成器
        g.manual_seed(self.seed + self.epoch)  # 设置种子（包含epoch信息）

        # 确定self.indices的最终大小（DDP索引）
        n = int((len(self.dataset) - self.rank - 1) / self.num_replicas) + 1  # num_replicas == WORLD_SIZE
        idx = torch.randperm(n, generator=g)  # 生成随机排列
        if not self.shuffle:
            idx = idx.sort()[0]  # 如果不洗牌则排序

        idx = idx.tolist()  # 转换为列表
        if self.drop_last:
            idx = idx[: self.num_samples]  # 丢弃最后不完整的批次
        else:
            # 填充到指定样本数
            padding_size = self.num_samples - len(idx)
            if padding_size <= len(idx):
                idx += idx[:padding_size]  # 重复前面的索引
            else:
                idx += (idx * math.ceil(padding_size / len(idx)))[:padding_size]  # 循环重复

        return iter(idx)


def create_dataloader(
    path,
    imgsz,
    batch_size,
    stride,
    single_cls=False,
    hyp=None,
    augment=False,
    cache=False,
    pad=0.0,
    rect=False,
    rank=-1,
    workers=8,
    image_weights=False,
    quad=False,
    prefix="",
    shuffle=False,
    seed=0,):
    """创建并返回配置好的DataLoader实例，用于加载和处理图像数据集。
    
    Args:
        path: 数据集路径
        imgsz: 图像尺寸
        batch_size: 批次大小
        stride: 模型步长
        single_cls: 是否为单类别检测
        hyp: 超参数字典
        augment: 是否启用数据增强
        cache: 是否缓存图像
        pad: 填充值
        rect: 是否使用矩形批次
        rank: 分布式训练中的进程排名
        workers: 数据加载工作进程数
        image_weights: 是否使用图像权重
        quad: 是否使用四倍批次大小
        prefix: 日志前缀
        shuffle: 是否打乱数据
        seed: 随机种子
        
    Returns:
        tuple: (DataLoader实例, 数据集实例)
    """
    if rect and shuffle:
        LOGGER.warning("WARNING ⚠️ --rect is incompatible with DataLoader shuffle, setting shuffle=False")
        shuffle = False  # 矩形批次与数据洗牌不兼容
        
    with torch_distributed_zero_first(rank):  # 在DDP中只初始化一次数据集缓存
        dataset = LoadImagesAndLabels(
            path,
            imgsz,
            batch_size,
            augment=augment,  # 数据增强
            hyp=hyp,  # 超参数
            rect=rect,  # 矩形批次
            cache_images=cache,  # 图像缓存
            single_cls=single_cls,  # 单类别
            stride=int(stride),  # 步长
            pad=pad,  # 填充
            image_weights=image_weights,  # 图像权重
            prefix=prefix,  # 前缀
            rank=rank,  # 进程排名
        )

    batch_size = min(batch_size, len(dataset))  # 确保批次大小不超过数据集大小
    nd = torch.cuda.device_count()  # CUDA设备数量
    nw = min([os.cpu_count() // max(nd, 1), batch_size if batch_size > 1 else 0, workers])  # 工作进程数
    sampler = None if rank == -1 else SmartDistributedSampler(dataset, shuffle=shuffle)  # 分布式采样器
    loader = DataLoader if image_weights else InfiniteDataLoader  # 选择数据加载器类型
    generator = torch.Generator()  # 随机数生成器
    generator.manual_seed(6148914691236517205 + seed + RANK)  # 设置随机种子
    return loader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle and sampler is None,  # 只有在没有采样器时才洗牌
        num_workers=nw,
        sampler=sampler,
        drop_last=quad,  # 四倍批次时丢弃最后不完整批次
        pin_memory=PIN_MEMORY,  # 内存固定
        collate_fn=LoadImagesAndLabels.collate_fn4 if quad else LoadImagesAndLabels.collate_fn,  # 批次整理函数
        worker_init_fn=seed_worker,  # 工作进程初始化函数
        generator=generator,  # 随机数生成器
    ), dataset


class InfiniteDataLoader(dataloader.DataLoader):
    """无限数据加载器，重复使用工作进程。
    
    这个数据加载器继承自标准的DataLoader，但会无限循环地提供数据批次，
    通过重复使用工作进程来提高效率。使用与标准DataLoader相同的语法。
    """

    def __init__(self, *args, **kwargs):
        """初始化无限数据加载器，重复使用工作进程，使用标准DataLoader语法。
        
        通过增加重复采样器来实现无限循环数据加载。
        """
        super().__init__(*args, **kwargs)
        object.__setattr__(self, "batch_sampler", _RepeatSampler(self.batch_sampler))  # 设置重复采样器
        self.iterator = super().__iter__()  # 创建迭代器

    def __len__(self):
        """返回无限数据加载器中批次采样器的采样器长度。
        
        Returns:
            int: 采样器长度
        """
        return len(self.batch_sampler.sampler)

    def __iter__(self):
        """无限循环地产生数据批次，当采样器耗尽时重置。
        
        Yields:
            批次数据
        """
        for _ in range(len(self)):
            yield next(self.iterator)


class _RepeatSampler:
    """永久重复的采样器。
    这个采样器会无限重复提供索引，用于实现无限数据加载。   
    Args:
        sampler (Sampler): 要包装的采样器实例
    """

    def __init__(self, sampler):
        """初始化永久采样器，包装提供的采样器实例以实现无限数据迭代。
        
        Args:
            sampler: 要包装的采样器
        """
        self.sampler = sampler  # 保存采样器引用

    def __iter__(self):
        """通过重复从给定采样器中产生数据来返回数据集的无限迭代器。
        
        Yields:
            采样器中的索引，无限循环
        """
        while True:
            yield from iter(self.sampler)  # 无限循环产生采样器中的索引





class LoadImages:
    """YOLOv5图像数据加载器。
    
    用于加载图像文件，支持单个文件、目录、glob模式和文件列表。
    例如：`python detect.py --source image.jpg`
    """

    def __init__(self, path, img_size=640, stride=32, auto=True, transforms=None):
        """初始化YOLOv5图像加载器。
        
        支持glob模式、目录和路径列表。
        
        Args:
            path: 文件路径、目录路径、glob模式或路径列表
            img_size: 图像尺寸
            stride: 模型步长
            auto: 是否自动调整
            transforms: 图像变换
        """
        if isinstance(path, str) and Path(path).suffix == ".txt":  # txt文件，每行一个图像路径
            path = Path(path).read_text().rsplit()
        files = []  # 文件列表
        for p in sorted(path) if isinstance(path, (list, tuple)) else [path]:
            p = str(Path(p).resolve())  # 解析为绝对路径
            if "*" in p:
                files.extend(sorted(glob.glob(p, recursive=True)))  # glob模式匹配
            elif os.path.isdir(p):
                files.extend(sorted(glob.glob(os.path.join(p, "*.*"))))  # 目录中的所有文件
            elif os.path.isfile(p):
                files.append(p)  # 单个文件
            else:
                raise FileNotFoundError(f"{p} does not exist")  # 路径不存在

        # 只保留图像文件
        self.files = [x for x in files if x.split(".")[-1].lower() in IMG_FORMATS]  # 图像文件
        self.nf = len(self.files)  # 文件总数

        self.img_size = img_size  # 图像尺寸
        self.stride = stride  # 步长
        self.mode = "image"  # 模式
        self.auto = auto  # 自动调整
        self.transforms = transforms  # 变换（可选）
        
        assert self.nf > 0, (
            f"No images found in {p}. Supported formats are:\nimages: {IMG_FORMATS}"
        )  # 确保找到了文件

    def __iter__(self):
        """通过重置计数初始化迭代器并返回迭代器对象本身。
        
        Returns:
            self: 返回自身实例
        """
        self.count = 0  # 重置计数
        return self

    def __next__(self):
        """前进到数据集中的下一个文件，如果到达末尾则引发StopIteration。
        
        Returns:
            tuple: (文件路径, 处理后图像, 原始图像, None, 描述字符串)
            
        Raises:
            StopIteration: 当到达数据集末尾时
        """
        if self.count == self.nf:
            raise StopIteration  # 到达末尾
        path = self.files[self.count]  # 当前文件路径

        # 读取图像
        self.count += 1  # 计数递增
        im0 = cv2.imread(path)  # 读取BGR图像
        assert im0 is not None, f"Image Not Found {path}"  # 确保图像存在
        s = f"image {self.count}/{self.nf} {path}: "  # 图像描述

        if self.transforms:
            im = self.transforms(im0)  # 应用变换
        else:
            im = letterbox(im0, self.img_size, stride=self.stride, auto=self.auto)[0]  # 填充调整大小
            im = im.transpose((2, 0, 1))[::-1]  # HWC转CHW，BGR转RGB
            im = np.ascontiguousarray(im)  # 确保内存连续

        return path, im, im0, None, s  # 返回路径、处理图像、原图、None、描述



    def __len__(self):
        """返回数据集中的文件数量。
        
        Returns:
            int: 文件总数
        """
        return self.nf  # 文件数量





def img2label_paths(img_paths):
    """从对应的图像文件路径生成标签文件路径，通过将`/images/`替换为`/labels/`并将扩展名替换为`.txt`。
    
    Args:
        img_paths: 图像文件路径列表
        
    Returns:
        list: 对应的标签文件路径列表
    """
    sa, sb = f"{os.sep}images{os.sep}", f"{os.sep}labels{os.sep}"  # /images/, /labels/ 子字符串
    return [sb.join(x.rsplit(sa, 1)).rsplit(".", 1)[0] + ".txt" for x in img_paths]  # 路径转换


class LoadImagesAndLabels(Dataset):
    """为YOLOv5的训练和验证加载图像及其对应的标签。
    
    这是YOLOv5的主要数据集类，负责：
    - 加载图像和标签文件
    - 缓存标签信息以提高性能
    - 处理数据增强（马赛克、混合等）
    - 支持矩形训练和图像权重
    """

    cache_version = 0.6  # 数据集标签*.cache版本
    rand_interp_methods = [cv2.INTER_NEAREST, cv2.INTER_LINEAR, cv2.INTER_CUBIC, cv2.INTER_AREA, cv2.INTER_LANCZOS4]  # 随机插值方法

    def __init__(
        self,
        path,
        img_size=640,
        batch_size=16,
        augment=False,
        hyp=None,
        rect=False,
        image_weights=False,
        cache_images=False,
        single_cls=False,
        stride=32,
        pad=0.0,
        min_items=0,
        prefix="",
        rank=-1,
        seed=0,
    ):
        """初始化YOLOv5数据集加载器，处理图像及其标签、缓存和预处理。
        
        Args:
            path: 数据集路径或图像文件列表
            img_size: 图像尺寸
            batch_size: 批次大小
            augment: 是否启用数据增强
            hyp: 超参数字典
            rect: 是否使用矩形训练
            image_weights: 是否使用图像权重
            cache_images: 是否缓存图像到内存
            single_cls: 是否单类训练
            stride: 模型步长
            pad: 填充值
            min_items: 最小项目数
            prefix: 日志前缀
            rank: 分布式训练rank
            seed: 随机种子
        """
        self.img_size = img_size  # 图像尺寸
        self.augment = augment  # 数据增强标志
        self.hyp = hyp  # 超参数
        self.image_weights = image_weights  # 图像权重标志
        self.rect = False if image_weights else rect  # 矩形训练（图像权重时禁用）
        self.mosaic = self.augment and not self.rect  # 马赛克增强（仅训练时一次加载4张图像）
        self.mosaic_border = [-img_size // 2, -img_size // 2]  # 马赛克边界
        self.stride = stride  # 模型步长
        self.path = path  # 数据集路径
        self.albumentations = Albumentations(size=img_size) if augment else None  # Albumentations增强

        try:
            f = []  # 图像文件列表
            for p in path if isinstance(path, list) else [path]:  # 遍历路径
                p = Path(p)  # 跨平台路径
                if p.is_dir():  # 如果是目录
                    f += glob.glob(str(p / "**" / "*.*"), recursive=True)  # 递归查找所有文件
                    # f = list(p.rglob('*.*'))  # pathlib方式
                elif p.is_file():  # 如果是文件
                    with open(p) as t:  # 打开文件
                        t = t.read().strip().splitlines()  # 读取行
                        parent = str(p.parent) + os.sep  # 父目录
                        f += [x.replace("./", parent, 1) if x.startswith("./") else x for x in t]  # 转换为全局路径
                        # f += [p.parent / x.lstrip(os.sep) for x in t]  # pathlib方式转换为全局路径
                else:
                    raise FileNotFoundError(f"{prefix}{p} does not exist")  # 路径不存在错误
            self.im_files = sorted(x.replace("/", os.sep) for x in f if x.split(".")[-1].lower() in IMG_FORMATS)  # 过滤图像文件
            # self.img_files = sorted([x for x in f if x.suffix[1:].lower() in IMG_FORMATS])  # pathlib方式
            assert self.im_files, f"{prefix}No images found"  # 确保找到图像
        except Exception as e:
            raise Exception(f"{prefix}Error loading data from {path}: {e}\n{HELP_URL}") from e  # 加载数据错误

        # 检查缓存
        self.label_files = img2label_paths(self.im_files)  # 标签文件路径
        cache_path = (p if p.is_file() else Path(self.label_files[0]).parent).with_suffix(".cache")  # 缓存路径
        try:
            cache, exists = np.load(cache_path, allow_pickle=True).item(), True  # 加载字典
            assert cache["version"] == self.cache_version  # 匹配当前版本
            assert cache["hash"] == get_hash(self.label_files + self.im_files)  # 相同哈希
        except Exception:
            cache, exists = self.cache_labels(cache_path, prefix), False  # 运行缓存操作

        # 显示缓存信息
        nf, nm, ne, nc, n = cache.pop("results")  # 找到、缺失、空、损坏、总计
        if exists and LOCAL_RANK in {-1, 0}:  # 如果缓存存在且为主进程
            d = f"Scanning {cache_path}... {nf} images, {nm + ne} backgrounds, {nc} corrupt"  # 描述信息
            tqdm(None, desc=prefix + d, total=n, initial=n, bar_format=TQDM_BAR_FORMAT)  # 显示缓存结果
            if cache["msgs"]:  # 如果有消息
                LOGGER.info("\n".join(cache["msgs"]))  # 显示警告
        assert nf > 0 or not augment, f"{prefix}No labels found in {cache_path}, can not start training. {HELP_URL}"  # 确保找到标签

        # 读取缓存
        [cache.pop(k) for k in ("hash", "version", "msgs")]  # 移除项目
        labels, shapes, self.segments = zip(*cache.values())  # 解压缓存值
        nl = len(np.concatenate(labels, 0))  # 标签数量
        assert nl > 0 or not augment, f"{prefix}All labels empty in {cache_path}, can not start training. {HELP_URL}"  # 确保标签非空
        self.labels = list(labels)  # 标签列表
        self.shapes = np.array(shapes)  # 形状数组
        self.im_files = list(cache.keys())  # 更新图像文件
        self.label_files = img2label_paths(cache.keys())  # 更新标签文件

        # 过滤图像
        if min_items:  # 如果设置了最小项目数
            include = np.array([len(x) >= min_items for x in self.labels]).nonzero()[0].astype(int)  # 包含的索引
            LOGGER.info(f"{prefix}{n - len(include)}/{n} images filtered from dataset")  # 过滤信息
            self.im_files = [self.im_files[i] for i in include]  # 过滤图像文件
            self.label_files = [self.label_files[i] for i in include]  # 过滤标签文件
            self.labels = [self.labels[i] for i in include]  # 过滤标签
            self.segments = [self.segments[i] for i in include]  # 过滤分割
            self.shapes = self.shapes[include]  # 过滤形状（宽高）

        # 创建索引
        n = len(self.shapes)  # 图像数量
        bi = np.floor(np.arange(n) / batch_size).astype(int)  # 批次索引
        nb = bi[-1] + 1  # 批次数量
        self.batch = bi  # 图像的批次索引
        self.n = n  # 图像总数
        self.indices = np.arange(n)  # 索引数组
        if rank > -1:  # DDP索引（参见：SmartDistributedSampler）
            # 强制每个rank（即GPU进程）在每个epoch采样相同的数据子集
            self.indices = self.indices[np.random.RandomState(seed=seed).permutation(n) % WORLD_SIZE == RANK]

        # Update labels
        include_class = []  # filter labels to include only these classes (optional)
        self.segments = list(self.segments)
        include_class_array = np.array(include_class).reshape(1, -1)
        for i, (label, segment) in enumerate(zip(self.labels, self.segments)):
            if include_class:
                j = (label[:, 0:1] == include_class_array).any(1)
                self.labels[i] = label[j]
                if segment:
                    self.segments[i] = [segment[idx] for idx, elem in enumerate(j) if elem]
            if single_cls:  # single-class training, merge all classes into 0
                self.labels[i][:, 0] = 0

        # Rectangular Training
        if self.rect:
            # Sort by aspect ratio
            s = self.shapes  # wh
            ar = s[:, 1] / s[:, 0]  # aspect ratio
            irect = ar.argsort()
            self.im_files = [self.im_files[i] for i in irect]
            self.label_files = [self.label_files[i] for i in irect]
            self.labels = [self.labels[i] for i in irect]
            self.segments = [self.segments[i] for i in irect]
            self.shapes = s[irect]  # wh
            ar = ar[irect]

            # Set training image shapes
            shapes = [[1, 1]] * nb
            for i in range(nb):
                ari = ar[bi == i]
                mini, maxi = ari.min(), ari.max()
                if maxi < 1:
                    shapes[i] = [maxi, 1]
                elif mini > 1:
                    shapes[i] = [1, 1 / mini]

            self.batch_shapes = np.ceil(np.array(shapes) * img_size / stride + pad).astype(int) * stride

        # Cache images into RAM/disk for faster training
        if cache_images == "ram" and not self.check_cache_ram(prefix=prefix):
            cache_images = False
        self.ims = [None] * n
        self.npy_files = [Path(f).with_suffix(".npy") for f in self.im_files]
        if cache_images:
            b, gb = 0, 1 << 30  # bytes of cached images, bytes per gigabytes
            self.im_hw0, self.im_hw = [None] * n, [None] * n
            fcn = self.cache_images_to_disk if cache_images == "disk" else self.load_image
            with ThreadPool(NUM_THREADS) as pool:
                results = pool.imap(lambda i: (i, fcn(i)), self.indices)
                pbar = tqdm(results, total=len(self.indices), bar_format=TQDM_BAR_FORMAT, disable=LOCAL_RANK > 0)
                for i, x in pbar:
                    if cache_images == "disk":
                        b += self.npy_files[i].stat().st_size
                    else:  # 'ram'
                        self.ims[i], self.im_hw0[i], self.im_hw[i] = x  # im, hw_orig, hw_resized = load_image(self, i)
                        b += self.ims[i].nbytes * WORLD_SIZE
                    pbar.desc = f"{prefix}Caching images ({b / gb:.1f}GB {cache_images})"
                pbar.close()

    def check_cache_ram(self, safety_margin=0.1, prefix=""):
        """Checks if available RAM is sufficient for caching images, adjusting for a safety margin."""
        b, gb = 0, 1 << 30  # bytes of cached images, bytes per gigabytes
        n = min(self.n, 30)  # extrapolate from 30 random images
        for _ in range(n):
            im = cv2.imread(random.choice(self.im_files))  # sample image
            ratio = self.img_size / max(im.shape[0], im.shape[1])  # max(h, w)  # ratio
            b += im.nbytes * ratio**2
        mem_required = b * self.n / n  # GB required to cache dataset into RAM
        mem = psutil.virtual_memory()
        cache = mem_required * (1 + safety_margin) < mem.available  # to cache or not to cache, that is the question
        if not cache:
            LOGGER.info(
                f"{prefix}{mem_required / gb:.1f}GB RAM required, "
                f"{mem.available / gb:.1f}/{mem.total / gb:.1f}GB available, "
                f"{'caching images ✅' if cache else 'not caching images ⚠️'}"
            )
        return cache

    def cache_labels(self, path=Path("./labels.cache"), prefix=""):
        """Caches dataset labels, verifies images, reads shapes, and tracks dataset integrity."""
        x = {}  # dict
        nm, nf, ne, nc, msgs = 0, 0, 0, 0, []  # number missing, found, empty, corrupt, messages
        desc = f"{prefix}Scanning {path.parent / path.stem}..."
        with Pool(NUM_THREADS) as pool:
            pbar = tqdm(
                pool.imap(verify_image_label, zip(self.im_files, self.label_files, repeat(prefix))),
                desc=desc,
                total=len(self.im_files),
                bar_format=TQDM_BAR_FORMAT,
            )
            for im_file, lb, shape, segments, nm_f, nf_f, ne_f, nc_f, msg in pbar:
                nm += nm_f
                nf += nf_f
                ne += ne_f
                nc += nc_f
                if im_file:
                    x[im_file] = [lb, shape, segments]
                if msg:
                    msgs.append(msg)
                pbar.desc = f"{desc} {nf} images, {nm + ne} backgrounds, {nc} corrupt"

        pbar.close()
        if msgs:
            LOGGER.info("\n".join(msgs))
        if nf == 0:
            LOGGER.warning(f"{prefix}WARNING ⚠️ No labels found in {path}. {HELP_URL}")
        x["hash"] = get_hash(self.label_files + self.im_files)
        x["results"] = nf, nm, ne, nc, len(self.im_files)
        x["msgs"] = msgs  # warnings
        x["version"] = self.cache_version  # cache version
        try:
            np.save(path, x)  # save cache for next time
            path.with_suffix(".cache.npy").rename(path)  # remove .npy suffix
            LOGGER.info(f"{prefix}New cache created: {path}")
        except Exception as e:
            LOGGER.warning(f"{prefix}WARNING ⚠️ Cache directory {path.parent} is not writeable: {e}")  # not writeable
        return x

    def __len__(self):
        """Returns the number of images in the dataset."""
        return len(self.im_files)



    def __getitem__(self, index):
        """Fetches the dataset item at the given index, considering linear, shuffled, or weighted sampling."""
        index = self.indices[index]  # linear, shuffled, or image_weights

        hyp = self.hyp
        if mosaic := self.mosaic and random.random() < hyp["mosaic"]:
            # Load mosaic
            img, labels = self.load_mosaic(index)
            shapes = None

            # MixUp augmentation
            if random.random() < hyp["mixup"]:
                img, labels = mixup(img, labels, *self.load_mosaic(random.choice(self.indices)))

        else:
            # Load image
            img, (h0, w0), (h, w) = self.load_image(index)

            # Letterbox
            shape = self.batch_shapes[self.batch[index]] if self.rect else self.img_size  # final letterboxed shape
            img, ratio, pad = letterbox(img, shape, auto=False, scaleup=self.augment)
            shapes = (h0, w0), ((h / h0, w / w0), pad)  # for COCO mAP rescaling

            labels = self.labels[index].copy()
            if labels.size:  # normalized xywh to pixel xyxy format
                labels[:, 1:] = xywhn2xyxy(labels[:, 1:], ratio[0] * w, ratio[1] * h, padw=pad[0], padh=pad[1])

            if self.augment:
                img, labels = random_perspective(
                    img,
                    labels,
                    degrees=hyp["degrees"],
                    translate=hyp["translate"],
                    scale=hyp["scale"],
                    shear=hyp["shear"],
                    perspective=hyp["perspective"],
                )

        nl = len(labels)  # number of labels
        if nl:
            labels[:, 1:5] = xyxy2xywhn(labels[:, 1:5], w=img.shape[1], h=img.shape[0], clip=True, eps=1e-3)

        if self.augment:
            # Albumentations
            img, labels = self.albumentations(img, labels)
            nl = len(labels)  # update after albumentations

            # HSV color-space
            augment_hsv(img, hgain=hyp["hsv_h"], sgain=hyp["hsv_s"], vgain=hyp["hsv_v"])

            # Flip up-down
            if random.random() < hyp["flipud"]:
                img = np.flipud(img)
                if nl:
                    labels[:, 2] = 1 - labels[:, 2]

            # Flip left-right
            if random.random() < hyp["fliplr"]:
                img = np.fliplr(img)
                if nl:
                    labels[:, 1] = 1 - labels[:, 1]

            # Cutouts
            # labels = cutout(img, labels, p=0.5)
            # nl = len(labels)  # update after cutout

        labels_out = torch.zeros((nl, 6))
        if nl:
            labels_out[:, 1:] = torch.from_numpy(labels)

        # Convert
        img = img.transpose((2, 0, 1))[::-1]  # HWC to CHW, BGR to RGB
        img = np.ascontiguousarray(img)

        return torch.from_numpy(img), labels_out, self.im_files[index], shapes

    def load_image(self, i):
        """
        Loads an image by index, returning the image, its original dimensions, and resized dimensions.

        Returns (im, original hw, resized hw)
        """
        im, f, fn = (
            self.ims[i],
            self.im_files[i],
            self.npy_files[i],
        )
        if im is None:  # not cached in RAM
            if fn.exists():  # load npy
                im = np.load(fn)
            else:  # read image
                im = cv2.imread(f)  # BGR
                assert im is not None, f"Image Not Found {f}"
            h0, w0 = im.shape[:2]  # orig hw
            r = self.img_size / max(h0, w0)  # ratio
            if r != 1:  # if sizes are not equal
                interp = cv2.INTER_LINEAR if (self.augment or r > 1) else cv2.INTER_AREA
                im = cv2.resize(im, (math.ceil(w0 * r), math.ceil(h0 * r)), interpolation=interp)
            return im, (h0, w0), im.shape[:2]  # im, hw_original, hw_resized
        return self.ims[i], self.im_hw0[i], self.im_hw[i]  # im, hw_original, hw_resized

    def cache_images_to_disk(self, i):
        """Saves an image to disk as an *.npy file for quicker loading, identified by index `i`."""
        f = self.npy_files[i]
        if not f.exists():
            np.save(f.as_posix(), cv2.imread(self.im_files[i]))

    def load_mosaic(self, index):
        """Loads a 4-image mosaic for YOLOv5, combining 1 selected and 3 random images, with labels and segments."""
        labels4, segments4 = [], []
        s = self.img_size
        yc, xc = (int(random.uniform(-x, 2 * s + x)) for x in self.mosaic_border)  # mosaic center x, y
        indices = [index] + random.choices(self.indices, k=3)  # 3 additional image indices
        random.shuffle(indices)
        for i, index in enumerate(indices):
            # Load image
            img, _, (h, w) = self.load_image(index)

            # place img in img4
            if i == 0:  # top left
                img4 = np.full((s * 2, s * 2, img.shape[2]), 114, dtype=np.uint8)  # base image with 4 tiles
                x1a, y1a, x2a, y2a = max(xc - w, 0), max(yc - h, 0), xc, yc  # xmin, ymin, xmax, ymax (large image)
                x1b, y1b, x2b, y2b = w - (x2a - x1a), h - (y2a - y1a), w, h  # xmin, ymin, xmax, ymax (small image)
            elif i == 1:  # top right
                x1a, y1a, x2a, y2a = xc, max(yc - h, 0), min(xc + w, s * 2), yc
                x1b, y1b, x2b, y2b = 0, h - (y2a - y1a), min(w, x2a - x1a), h
            elif i == 2:  # bottom left
                x1a, y1a, x2a, y2a = max(xc - w, 0), yc, xc, min(s * 2, yc + h)
                x1b, y1b, x2b, y2b = w - (x2a - x1a), 0, w, min(y2a - y1a, h)
            elif i == 3:  # bottom right
                x1a, y1a, x2a, y2a = xc, yc, min(xc + w, s * 2), min(s * 2, yc + h)
                x1b, y1b, x2b, y2b = 0, 0, min(w, x2a - x1a), min(y2a - y1a, h)

            img4[y1a:y2a, x1a:x2a] = img[y1b:y2b, x1b:x2b]  # img4[ymin:ymax, xmin:xmax]
            padw = x1a - x1b
            padh = y1a - y1b

            # Labels
            labels, segments = self.labels[index].copy(), self.segments[index].copy()
            if labels.size:
                labels[:, 1:] = xywhn2xyxy(labels[:, 1:], w, h, padw, padh)  # normalized xywh to pixel xyxy format
                segments = [xyn2xy(x, w, h, padw, padh) for x in segments]
            labels4.append(labels)
            segments4.extend(segments)

        # Concat/clip labels
        labels4 = np.concatenate(labels4, 0)
        for x in (labels4[:, 1:], *segments4):
            np.clip(x, 0, 2 * s, out=x)  # clip when using random_perspective()
        # img4, labels4 = replicate(img4, labels4)  # replicate

        # Augment
        img4, labels4, segments4 = copy_paste(img4, labels4, segments4, p=self.hyp["copy_paste"])
        img4, labels4 = random_perspective(
            img4,
            labels4,
            segments4,
            degrees=self.hyp["degrees"],
            translate=self.hyp["translate"],
            scale=self.hyp["scale"],
            shear=self.hyp["shear"],
            perspective=self.hyp["perspective"],
            border=self.mosaic_border,
        )  # border to remove

        return img4, labels4

    def load_mosaic9(self, index):
        """Loads 1 image + 8 random images into a 9-image mosaic for augmented YOLOv5 training, returning labels and
        segments.
        """
        labels9, segments9 = [], []
        s = self.img_size
        indices = [index] + random.choices(self.indices, k=8)  # 8 additional image indices
        random.shuffle(indices)
        hp, wp = -1, -1  # height, width previous
        for i, index in enumerate(indices):
            # Load image
            img, _, (h, w) = self.load_image(index)

            # place img in img9
            if i == 0:  # center
                img9 = np.full((s * 3, s * 3, img.shape[2]), 114, dtype=np.uint8)  # base image with 4 tiles
                h0, w0 = h, w
                c = s, s, s + w, s + h  # xmin, ymin, xmax, ymax (base) coordinates
            elif i == 1:  # top
                c = s, s - h, s + w, s
            elif i == 2:  # top right
                c = s + wp, s - h, s + wp + w, s
            elif i == 3:  # right
                c = s + w0, s, s + w0 + w, s + h
            elif i == 4:  # bottom right
                c = s + w0, s + hp, s + w0 + w, s + hp + h
            elif i == 5:  # bottom
                c = s + w0 - w, s + h0, s + w0, s + h0 + h
            elif i == 6:  # bottom left
                c = s + w0 - wp - w, s + h0, s + w0 - wp, s + h0 + h
            elif i == 7:  # left
                c = s - w, s + h0 - h, s, s + h0
            elif i == 8:  # top left
                c = s - w, s + h0 - hp - h, s, s + h0 - hp

            padx, pady = c[:2]
            x1, y1, x2, y2 = (max(x, 0) for x in c)  # allocate coords

            # Labels
            labels, segments = self.labels[index].copy(), self.segments[index].copy()
            if labels.size:
                labels[:, 1:] = xywhn2xyxy(labels[:, 1:], w, h, padx, pady)  # normalized xywh to pixel xyxy format
                segments = [xyn2xy(x, w, h, padx, pady) for x in segments]
            labels9.append(labels)
            segments9.extend(segments)

            # Image
            img9[y1:y2, x1:x2] = img[y1 - pady :, x1 - padx :]  # img9[ymin:ymax, xmin:xmax]
            hp, wp = h, w  # height, width previous

        # Offset
        yc, xc = (int(random.uniform(0, s)) for _ in self.mosaic_border)  # mosaic center x, y
        img9 = img9[yc : yc + 2 * s, xc : xc + 2 * s]

        # Concat/clip labels
        labels9 = np.concatenate(labels9, 0)
        labels9[:, [1, 3]] -= xc
        labels9[:, [2, 4]] -= yc
        c = np.array([xc, yc])  # centers
        segments9 = [x - c for x in segments9]

        for x in (labels9[:, 1:], *segments9):
            np.clip(x, 0, 2 * s, out=x)  # clip when using random_perspective()
        # img9, labels9 = replicate(img9, labels9)  # replicate

        # Augment
        img9, labels9, segments9 = copy_paste(img9, labels9, segments9, p=self.hyp["copy_paste"])
        img9, labels9 = random_perspective(
            img9,
            labels9,
            segments9,
            degrees=self.hyp["degrees"],
            translate=self.hyp["translate"],
            scale=self.hyp["scale"],
            shear=self.hyp["shear"],
            perspective=self.hyp["perspective"],
            border=self.mosaic_border,
        )  # border to remove

        return img9, labels9

    @staticmethod
    def collate_fn(batch):
        """Batches images, labels, paths, and shapes, assigning unique indices to targets in merged label tensor."""
        im, label, path, shapes = zip(*batch)  # transposed
        for i, lb in enumerate(label):
            lb[:, 0] = i  # add target image index for build_targets()
        return torch.stack(im, 0), torch.cat(label, 0), path, shapes

    @staticmethod
    def collate_fn4(batch):
        """Bundles a batch's data by quartering the number of shapes and paths, preparing it for model input."""
        im, label, path, shapes = zip(*batch)  # transposed
        n = len(shapes) // 4
        im4, label4, path4, shapes4 = [], [], path[:n], shapes[:n]

        ho = torch.tensor([[0.0, 0, 0, 1, 0, 0]])
        wo = torch.tensor([[0.0, 0, 1, 0, 0, 0]])
        s = torch.tensor([[1, 1, 0.5, 0.5, 0.5, 0.5]])  # scale
        for i in range(n):  # zidane torch.zeros(16,3,720,1280)  # BCHW
            i *= 4
            if random.random() < 0.5:
                im1 = F.interpolate(im[i].unsqueeze(0).float(), scale_factor=2.0, mode="bilinear", align_corners=False)[
                    0
                ].type(im[i].type())
                lb = label[i]
            else:
                im1 = torch.cat((torch.cat((im[i], im[i + 1]), 1), torch.cat((im[i + 2], im[i + 3]), 1)), 2)
                lb = torch.cat((label[i], label[i + 1] + ho, label[i + 2] + wo, label[i + 3] + ho + wo), 0) * s
            im4.append(im1)
            label4.append(lb)

        for i, lb in enumerate(label4):
            lb[:, 0] = i  # add target image index for build_targets()

        return torch.stack(im4, 0), torch.cat(label4, 0), path4, shapes4


# Ancillary functions --------------------------------------------------------------------------------------------------
def flatten_recursive(path=DATASETS_DIR / "coco128"):
    """Flattens a directory by copying all files from subdirectories to a new top-level directory, preserving
    filenames.
    """
    new_path = Path(f"{str(path)}_flat")
    if os.path.exists(new_path):
        shutil.rmtree(new_path)  # delete output folder
    os.makedirs(new_path)  # make new output folder
    for file in tqdm(glob.glob(f"{str(Path(path))}/**/*.*", recursive=True)):
        shutil.copyfile(file, new_path / Path(file).name)





def autosplit(path=DATASETS_DIR / "coco128/images", weights=(0.9, 0.1, 0.0), annotated_only=False):
    """Autosplit a dataset into train/val/test splits and save path/autosplit_*.txt files
    Usage: from utils.dataloaders import *; autosplit().

    Arguments:
        path:            Path to images directory
        weights:         Train, val, test weights (list, tuple)
        annotated_only:  Only use images with an annotated txt file
    """
    path = Path(path)  # images dir
    files = sorted(x for x in path.rglob("*.*") if x.suffix[1:].lower() in IMG_FORMATS)  # image files only
    n = len(files)  # number of files
    random.seed(0)  # for reproducibility
    indices = random.choices([0, 1, 2], weights=weights, k=n)  # assign each image to a split

    txt = ["autosplit_train.txt", "autosplit_val.txt", "autosplit_test.txt"]  # 3 txt files
    for x in txt:
        if (path.parent / x).exists():
            (path.parent / x).unlink()  # remove existing

    print(f"Autosplitting images from {path}" + ", using *.txt labeled images only" * annotated_only)
    for i, img in tqdm(zip(indices, files), total=n):
        if not annotated_only or Path(img2label_paths([str(img)])[0]).exists():  # check label
            with open(path.parent / txt[i], "a") as f:
                f.write(f"./{img.relative_to(path.parent).as_posix()}" + "\n")  # add image to txt file


def verify_image_label(args):
    """Verifies a single image-label pair, ensuring image format, size, and legal label values."""
    im_file, lb_file, prefix = args
    nm, nf, ne, nc, msg, segments = 0, 0, 0, 0, "", []  # number (missing, found, empty, corrupt), message, segments
    try:
        # verify images
        im = Image.open(im_file)
        im.verify()  # PIL verify
        shape = exif_size(im)  # image size
        assert (shape[0] > 9) & (shape[1] > 9), f"image size {shape} <10 pixels"
        assert im.format.lower() in IMG_FORMATS, f"invalid image format {im.format}"
        if im.format.lower() in ("jpg", "jpeg"):
            with open(im_file, "rb") as f:
                f.seek(-2, 2)
                if f.read() != b"\xff\xd9":  # corrupt JPEG
                    ImageOps.exif_transpose(Image.open(im_file)).save(im_file, "JPEG", subsampling=0, quality=100)
                    msg = f"{prefix}WARNING ⚠️ {im_file}: corrupt JPEG restored and saved"

        # verify labels
        if os.path.isfile(lb_file):
            nf = 1  # label found
            with open(lb_file) as f:
                lb = [x.split() for x in f.read().strip().splitlines() if len(x)]
                if any(len(x) > 6 for x in lb):  # is segment
                    classes = np.array([x[0] for x in lb], dtype=np.float32)
                    segments = [np.array(x[1:], dtype=np.float32).reshape(-1, 2) for x in lb]  # (cls, xy1...)
                    lb = np.concatenate((classes.reshape(-1, 1), segments2boxes(segments)), 1)  # (cls, xywh)
                lb = np.array(lb, dtype=np.float32)
            if nl := len(lb):
                assert lb.shape[1] == 5, f"labels require 5 columns, {lb.shape[1]} columns detected"
                assert (lb >= 0).all(), f"negative label values {lb[lb < 0]}"
                assert (lb[:, 1:] <= 1).all(), f"non-normalized or out of bounds coordinates {lb[:, 1:][lb[:, 1:] > 1]}"
                _, i = np.unique(lb, axis=0, return_index=True)
                if len(i) < nl:  # duplicate row check
                    lb = lb[i]  # remove duplicates
                    if segments:
                        segments = [segments[x] for x in i]
                    msg = f"{prefix}WARNING ⚠️ {im_file}: {nl - len(i)} duplicate labels removed"
            else:
                ne = 1  # label empty
                lb = np.zeros((0, 5), dtype=np.float32)
        else:
            nm = 1  # label missing
            lb = np.zeros((0, 5), dtype=np.float32)
        return im_file, lb, shape, segments, nm, nf, ne, nc, msg
    except Exception as e:
        nc = 1
        msg = f"{prefix}WARNING ⚠️ {im_file}: ignoring corrupt image/label: {e}"
        return [None, None, None, None, nm, nf, ne, nc, msg]


class HUBDatasetStats:
    """
    Class for generating HUB dataset JSON and `-hub` dataset directory.

    Arguments:
        path:           Path to data.yaml or data.zip (with data.yaml inside data.zip)
        autodownload:   Attempt to download dataset if not found locally

    Usage
        from utils.dataloaders import HUBDatasetStats
        stats = HUBDatasetStats('coco128.yaml', autodownload=True)  # usage 1
        stats = HUBDatasetStats('path/to/coco128.zip')  # usage 2
        stats.get_json(save=False)
        stats.process_images()
    """

    def __init__(self, path="coco128.yaml", autodownload=False):
        """Initializes HUBDatasetStats with optional auto-download for datasets, given a path to dataset YAML or ZIP
        file.
        """
        zipped, data_dir, yaml_path = self._unzip(Path(path))
        try:
            with open(check_yaml(yaml_path), errors="ignore") as f:
                data = yaml.safe_load(f)  # data dict
                if zipped:
                    data["path"] = data_dir
        except Exception as e:
            raise Exception("error/HUB/dataset_stats/yaml_load") from e

        check_dataset(data, autodownload)  # download dataset if missing
        self.hub_dir = Path(data["path"] + "-hub")
        self.im_dir = self.hub_dir / "images"
        self.im_dir.mkdir(parents=True, exist_ok=True)  # makes /images
        self.stats = {"nc": data["nc"], "names": list(data["names"].values())}  # statistics dictionary
        self.data = data

    @staticmethod
    def _find_yaml(dir):
        """Finds and returns the path to a single '.yaml' file in the specified directory, preferring files that match
        the directory name.
        """
        files = list(dir.glob("*.yaml")) or list(dir.rglob("*.yaml"))  # try root level first and then recursive
        assert files, f"No *.yaml file found in {dir}"
        if len(files) > 1:
            files = [f for f in files if f.stem == dir.stem]  # prefer *.yaml files that match dir name
            assert files, f"Multiple *.yaml files found in {dir}, only 1 *.yaml file allowed"
        assert len(files) == 1, f"Multiple *.yaml files found: {files}, only 1 *.yaml file allowed in {dir}"
        return files[0]

    def _unzip(self, path):
        """Unzips a .zip file at 'path', returning success status, unzipped directory, and path to YAML file within."""
        if not str(path).endswith(".zip"):  # path is data.yaml
            return False, None, path
        assert Path(path).is_file(), f"Error unzipping {path}, file not found"
        unzip_file(path, path=path.parent)
        dir = path.with_suffix("")  # dataset directory == zip name
        assert dir.is_dir(), f"Error unzipping {path}, {dir} not found. path/to/abc.zip MUST unzip to path/to/abc/"
        return True, str(dir), self._find_yaml(dir)  # zipped, data_dir, yaml_path

    def _hub_ops(self, f, max_dim=1920):
        """Resizes and saves an image at reduced quality for web/app viewing, supporting both PIL and OpenCV."""
        f_new = self.im_dir / Path(f).name  # dataset-hub image filename
        try:  # use PIL
            im = Image.open(f)
            r = max_dim / max(im.height, im.width)  # ratio
            if r < 1.0:  # image too large
                im = im.resize((int(im.width * r), int(im.height * r)))
            im.save(f_new, "JPEG", quality=50, optimize=True)  # save
        except Exception as e:  # use OpenCV
            LOGGER.info(f"WARNING ⚠️ HUB ops PIL failure {f}: {e}")
            im = cv2.imread(f)
            im_height, im_width = im.shape[:2]
            r = max_dim / max(im_height, im_width)  # ratio
            if r < 1.0:  # image too large
                im = cv2.resize(im, (int(im_width * r), int(im_height * r)), interpolation=cv2.INTER_AREA)
            cv2.imwrite(str(f_new), im)

    def get_json(self, save=False, verbose=False):
        """Generates dataset JSON for Ultralytics HUB, optionally saves or prints it; save=bool, verbose=bool."""

        def _round(labels):
            """Rounds class labels to integers and coordinates to 4 decimal places for improved label accuracy."""
            return [[int(c), *(round(x, 4) for x in points)] for c, *points in labels]

        for split in "train", "val", "test":
            if self.data.get(split) is None:
                self.stats[split] = None  # i.e. no test set
                continue
            dataset = LoadImagesAndLabels(self.data[split])  # load dataset
            x = np.array(
                [
                    np.bincount(label[:, 0].astype(int), minlength=self.data["nc"])
                    for label in tqdm(dataset.labels, total=dataset.n, desc="Statistics")
                ]
            )  # shape(128x80)
            self.stats[split] = {
                "instance_stats": {"total": int(x.sum()), "per_class": x.sum(0).tolist()},
                "image_stats": {
                    "total": dataset.n,
                    "unlabelled": int(np.all(x == 0, 1).sum()),
                    "per_class": (x > 0).sum(0).tolist(),
                },
                "labels": [{str(Path(k).name): _round(v.tolist())} for k, v in zip(dataset.im_files, dataset.labels)],
            }

        # Save, print and return
        if save:
            stats_path = self.hub_dir / "stats.json"
            print(f"Saving {stats_path.resolve()}...")
            with open(stats_path, "w") as f:
                json.dump(self.stats, f)  # save stats.json
        if verbose:
            print(json.dumps(self.stats, indent=2, sort_keys=False))
        return self.stats

    def process_images(self):
        """Compresses images for Ultralytics HUB across 'train', 'val', 'test' splits and saves to specified
        directory.
        """
        for split in "train", "val", "test":
            if self.data.get(split) is None:
                continue
            dataset = LoadImagesAndLabels(self.data[split])  # load dataset
            desc = f"{split} images"
            for _ in tqdm(ThreadPool(NUM_THREADS).imap(self._hub_ops, dataset.im_files), total=dataset.n, desc=desc):
                pass
        print(f"Done. All images saved to {self.im_dir}")
        return self.im_dir
