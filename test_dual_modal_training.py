#!/usr/bin/env python3
"""
双模态训练脚本测试

测试双模态训练脚本的关键功能：
1. 命令行参数解析
2. 双模态数据加载器创建
3. 双模态模型初始化
4. 数据格式验证
"""

import sys
import torch
import argparse
from pathlib import Path

# 添加项目根目录到路径
FILE = Path(__file__).resolve()
ROOT = FILE.parent
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))

from utils.segment.dataloaders import create_multimodal_dataloader
from models.yolo import SegmentationModel
from utils.general import colorstr, LOGGER


def test_dual_modal_args():
    """测试双模态命令行参数解析"""
    print(f"{colorstr('blue', 'bold', '测试1: 双模态命令行参数解析')}")
    
    try:
        # 直接测试参数解析器而不导入整个训练模块
        import argparse
        
        # 创建参数解析器（模拟train.py中的参数定义）
        parser = argparse.ArgumentParser()
        parser.add_argument('--multimodal', action='store_true', help='Enable dual-modal training')
        parser.add_argument('--xpl-path', type=str, default='', help='Path to XPL images')
        parser.add_argument('--cfg', type=str, default='', help='Model configuration file')
        parser.add_argument('--data', type=str, default='', help='Dataset configuration file')
        
        # 模拟命令行参数
        test_args = [
            '--multimodal',
            '--xpl-path', 'dataset-seg/images_xpl',
            '--cfg', 'models/segment/yolov5n-mid-seg.yaml',
            '--data', 'dataset-seg/datasets.yaml'
        ]
        
        # 解析参数
        opt = parser.parse_args(test_args)
        
        # 验证双模态参数
        assert hasattr(opt, 'multimodal'), "缺少 multimodal 参数"
        assert hasattr(opt, 'xpl_path'), "缺少 xpl_path 参数"
        assert opt.multimodal == True, "multimodal 参数应为 True"
        assert opt.xpl_path == 'dataset-seg/images_xpl', "xpl_path 参数值不正确"
        
        print(f"  ✓ multimodal: {opt.multimodal}")
        print(f"  ✓ xpl_path: {opt.xpl_path}")
        print(f"  ✓ cfg: {opt.cfg}")
        print(f"  ✓ data: {opt.data}")
        
        return True
        
    except Exception as e:
        print(f"  ✗ 参数解析失败: {e}")
        return False


def test_dual_modal_model_init():
    """测试双模态模型初始化"""
    print(f"\n{colorstr('blue', 'bold', '测试2: 双模态模型初始化')}")
    
    try:
        # 测试双模态模型配置
        cfg_path = 'models/segment/yolov5n-mid-seg.yaml'
        if not Path(cfg_path).exists():
            print(f"  ⚠ 配置文件不存在: {cfg_path}，跳过模型初始化测试")
            return True
            
        # 使用InputRouter架构的双模态模型，ch=3表示每个模态的通道数
        model_dual = SegmentationModel(cfg_path, ch=3, nc=1)
        print(f"  ✓ 双模态模型创建成功 (使用InputRouter架构)")
        print(f"  ✓ 模型参数数量: {sum(p.numel() for p in model_dual.parameters()):,}")
        
        # 检查模型是否包含InputRouter
        has_input_router = False
        for module in model_dual.modules():
            if 'InputRouter' in str(type(module)):
                has_input_router = True
                break
        
        if has_input_router:
            print(f"  ✓ 模型包含InputRouter组件")
        else:
            print(f"  ⚠ 模型不包含InputRouter组件，可能使用不同的架构")
        
        # 测试模型能否接受双模态输入（InputRouter期望多输入格式）
        # 创建两个3通道输入模拟RGB和X模态
        rgb_input = torch.randn(1, 3, 256, 256)
        x_input = torch.randn(1, 3, 256, 256)
        dual_input = (rgb_input, x_input)  # InputRouter期望的tuple格式
        
        with torch.no_grad():
            try:
                output = model_dual(dual_input)
                print(f"  ✓ 模型成功处理双模态输入 (RGB + X)")
                print(f"  ✓ 输出数量: {len(output)}")
            except Exception as e:
                print(f"  ✗ 模型无法处理双模态输入: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"  ✗ 模型初始化失败: {e}")
        return False


def test_data_format():
    """测试数据格式"""
    print(f"\n{colorstr('blue', 'bold', '测试3: 数据格式验证')}")
    
    try:
        # 创建模拟的双模态数据
        batch_size = 2
        img_size = 256  # 使用较小的图像尺寸进行测试
        
        # 模拟PPL图像 (RGB, 3通道)
        ppl_imgs = torch.randn(batch_size, 3, img_size, img_size)
        print(f"  ✓ PPL图像形状: {ppl_imgs.shape}")
        
        # 模拟XPL图像 (RGB, 3通道)
        xpl_imgs = torch.randn(batch_size, 3, img_size, img_size)
        print(f"  ✓ XPL图像形状: {xpl_imgs.shape}")
        
        # 模拟双模态数据拼接（数据加载器返回的格式）
        dual_modal_imgs = torch.cat([ppl_imgs, xpl_imgs], dim=1)
        print(f"  ✓ 双模态拼接后形状: {dual_modal_imgs.shape}")
        
        # 验证拼接后的形状
        expected_shape = (batch_size, 6, img_size, img_size)
        assert dual_modal_imgs.shape == expected_shape, f"期望形状 {expected_shape}，实际形状 {dual_modal_imgs.shape}"
        
        # 测试模型前向传播（如果模型可用）
        cfg_path = 'models/segment/yolov5n-mid-seg.yaml'
        if Path(cfg_path).exists():
            # 注意：这个配置使用InputRouter，ch参数应该是3（每个模态的通道数）
            model = SegmentationModel(cfg_path, ch=3, nc=1)
            model.eval()
            
                # 将6通道数据分离为RGB和X模态（模拟数据预处理）
            rgb_imgs = dual_modal_imgs[:, :3, :, :]  # 前3通道为RGB
            x_imgs = dual_modal_imgs[:, 3:, :, :]    # 后3通道为X
            dual_modal_input = (rgb_imgs, x_imgs)    # InputRouter期望的格式
            
            with torch.no_grad():
                # 使用分离后的双模态输入
                output = model(dual_modal_input)
                print(f"  ✓ 模型前向传播成功，输出数量: {len(output)}")
                for i, out in enumerate(output):
                    if hasattr(out, 'shape'):
                        print(f"    - 输出 {i} 形状: {out.shape}")
                    else:
                        print(f"    - 输出 {i} 类型: {type(out)}")
                print(f"  ✓ 数据格式转换: 6通道 -> (RGB: {rgb_imgs.shape}, X: {x_imgs.shape})")
                print(f"  ✓ 双模态模型成功处理输入数据")
        else:
            print(f"  ⚠ 配置文件不存在，跳过模型前向传播测试")
        
        return True
        
    except Exception as e:
        print(f"  ✗ 数据格式测试失败: {e}")
        return False


def test_dataloader_compatibility():
    """测试数据加载器兼容性"""
    print(f"\n{colorstr('blue', 'bold', '测试4: 数据加载器兼容性')}")
    
    try:
        # 检查create_multimodal_dataloader函数是否可导入
        from utils.segment.dataloaders import create_multimodal_dataloader
        print(f"  ✓ create_multimodal_dataloader 导入成功")
        
        # 检查函数签名
        import inspect
        sig = inspect.signature(create_multimodal_dataloader)
        params = list(sig.parameters.keys())
        
        required_params = ['multimodal', 'xpl_path']
        for param in required_params:
            if param in params:
                print(f"  ✓ 参数 '{param}' 存在")
            else:
                print(f"  ✗ 参数 '{param}' 缺失")
                return False
        
        return True
        
    except ImportError as e:
        print(f"  ✗ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"  ✗ 兼容性测试失败: {e}")
        return False


def main():
    """运行所有测试"""
    print(f"{colorstr('green', 'bold', '双模态训练脚本测试')}")
    print("=" * 50)
    
    tests = [
        test_dual_modal_args,
        test_dual_modal_model_init,
        test_data_format,
        test_dataloader_compatibility
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"  ✗ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"{colorstr('green', 'bold', '测试结果:')} {passed}/{total} 通过")
    
    if passed == total:
        print(f"{colorstr('green', '✓ 所有测试通过！双模态训练脚本修改成功。')}")
        return True
    else:
        print(f"{colorstr('red', '✗ 部分测试失败，请检查修改。')}")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)