
"""图像增强函数 (Image augmentation functions)."""

import math
import random
import cv2
import numpy as np

from ..augmentations import box_candidates  # 用于筛选合法框
from ..general import resample_segments, segment2box  # 用于分割点采样和转换为bbox


def mixup(im, labels, segments, im2, labels2, segments2, im_xpl=None, im2_xpl=None):
    """
    双模态mixup增强函数
    
    Args:
        im: PPL图像1
        labels: 图像1的标签
        segments: 图像1的分割信息
        im2: PPL图像2
        labels2: 图像2的标签
        segments2: 图像2的分割信息
        im_xpl: XPL图像1 (可选)
        im2_xpl: XPL图像2 (可选)
    
    Returns:
        如果提供XPL图像: (im, labels, segments, im_xpl)
        否则: (im, labels, segments)
    """
    r = np.random.beta(32.0, 32.0)  # beta分布采样，决定mixup比例
    im = (im * r + im2 * (1 - r)).astype(np.uint8)  # 图像加权融合
    
    # 如果提供了XPL图像，应用相同的mixup比例
    if im_xpl is not None and im2_xpl is not None:
        im_xpl = (im_xpl * r + im2_xpl * (1 - r)).astype(np.uint8)
    
    labels = np.concatenate((labels, labels2), 0)  # 合并两张图的标签
    segments = np.concatenate((segments, segments2), 0)  # 合并分割信息
    
    if im_xpl is not None:
        return im, labels, segments, im_xpl
    return im, labels, segments


def random_perspective(
    im, targets=(), segments=(), degrees=10, translate=0.1, scale=0.1, shear=10, perspective=0.0, border=(0, 0), im_xpl=None
):
    """
    双模态随机透视变换（Random Perspective）
    - 作用：对图像和标签应用随机旋转、平移、缩放、错切、透视等增强操作。
    - 参数：
        im: PPL输入图像
        targets: 目标框 [cls, x1, y1, x2, y2]
        segments: 多边形分割点
        degrees: 最大旋转角度
        translate: 平移比例
        scale: 缩放比例
        shear: 错切角度
        perspective: 透视比例
        border: 图像边框扩展
        im_xpl: XPL输入图像 (可选)
    
    Returns:
        如果提供XPL图像: (im, targets, new_segments, im_xpl)
        否则: (im, targets, new_segments)
    """
    height = im.shape[0] + border[0] * 2  # 新图像高度
    width = im.shape[1] + border[1] * 2   # 新图像宽度

    # ----------------- 构建变换矩阵 -----------------
    # 1) 平移到图像中心
    C = np.eye(3)
    C[0, 2] = -im.shape[1] / 2
    C[1, 2] = -im.shape[0] / 2

    # 2) 透视变换
    P = np.eye(3)
    P[2, 0] = random.uniform(-perspective, perspective)  # x方向透视
    P[2, 1] = random.uniform(-perspective, perspective)  # y方向透视

    # 3) 旋转+缩放
    R = np.eye(3)
    a = random.uniform(-degrees, degrees)  # 随机旋转角度
    s = random.uniform(1 - scale, 1 + scale)  # 随机缩放比例
    R[:2] = cv2.getRotationMatrix2D(angle=a, center=(0, 0), scale=s)

    # 4) 错切（shear）
    S = np.eye(3)
    S[0, 1] = math.tan(random.uniform(-shear, shear) * math.pi / 180)  # x方向错切
    S[1, 0] = math.tan(random.uniform(-shear, shear) * math.pi / 180)  # y方向错切

    # 5) 平移（translation）
    T = np.eye(3)
    T[0, 2] = random.uniform(0.5 - translate, 0.5 + translate) * width
    T[1, 2] = random.uniform(0.5 - translate, 0.5 + translate) * height

    # 6) 合并所有变换矩阵
    M = T @ S @ R @ P @ C  # 注意顺序（右到左）

    # ----------------- 图像变换 -----------------
    if (border[0] != 0) or (border[1] != 0) or (M != np.eye(3)).any():
        if perspective:  # 透视变换
            im = cv2.warpPerspective(im, M, dsize=(width, height), borderValue=(114, 114, 114))
            # 如果提供了XPL图像，应用相同的透视变换
            if im_xpl is not None:
                im_xpl = cv2.warpPerspective(im_xpl, M, dsize=(width, height), borderValue=(114, 114, 114))
        else:  # 仿射变换
            im = cv2.warpAffine(im, M[:2], dsize=(width, height), borderValue=(114, 114, 114))
            # 如果提供了XPL图像，应用相同的仿射变换
            if im_xpl is not None:
                im_xpl = cv2.warpAffine(im_xpl, M[:2], dsize=(width, height), borderValue=(114, 114, 114))

    # ----------------- 标签变换 -----------------
    new_segments = []
    if n := len(targets):  # 如果有目标
        new = np.zeros((n, 4))
        segments = resample_segments(segments)  # 上采样分割点，提高精度
        for i, segment in enumerate(segments):
            xy = np.ones((len(segment), 3))  # 构建齐次坐标
            xy[:, :2] = segment
            xy = xy @ M.T  # 应用变换矩阵
            xy = xy[:, :2] / xy[:, 2:3] if perspective else xy[:, :2]  # 透视归一化或仿射

            # 转换成 bbox
            new[i] = segment2box(xy, width, height)
            new_segments.append(xy)

        # 过滤掉过小或无效的目标框
        i = box_candidates(box1=targets[:, 1:5].T * s, box2=new.T, area_thr=0.01)
        targets = targets[i]
        targets[:, 1:5] = new[i]
        new_segments = np.array(new_segments)[i]

    if im_xpl is not None:
        return im, targets, new_segments, im_xpl
    return im, targets, new_segments
