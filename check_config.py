import yaml
import os

with open('dataset-seg/datasets.yaml', 'r', encoding='utf-8') as f:
    data = yaml.safe_load(f)

print('train_xpl:', data.get('train_xpl', 'NOT_FOUND'))
print('multimodal:', data.get('multimodal', 'NOT_FOUND'))

xpl_path = os.path.join(data['path'], data.get('train_xpl', ''))
print('XPL path exists:', os.path.exists(xpl_path))
print('XPL path:', xpl_path)
print('XPL path is empty string:', repr(data.get('train_xpl', '')) == "''")
print('XPL path bool value:', bool(data.get('train_xpl', '')))