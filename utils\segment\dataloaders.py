# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license
"""数据加载器模块 - 用于YOLOv5分割模型的数据加载和预处理。"""

import os
import random
from pathlib import Path

import cv2
import numpy as np
import torch
import torch.nn.functional as F
from torch.utils.data import DataLoader

# 导入数据增强相关模块
from ..augmentations import augment_hsv, copy_paste, letterbox
from ..dataloaders import InfiniteDataLoader, LoadImagesAndLabels, SmartDistributedSampler, seed_worker
from ..general import LOGGER, xyn2xy, xywhn2xyxy, xyxy2xywhn
from ..torch_utils import torch_distributed_zero_first
from .augmentations import mixup, random_perspective

# 获取分布式训练的进程排名
RANK = int(os.getenv("RANK", -1))


def create_dataloader(
    path,                    # 数据集路径
    imgsz,                   # 图像尺寸
    batch_size,              # 批次大小
    stride,                  # 模型步长
    single_cls=False,        # 是否为单类别检测
    hyp=None,                # 超参数字典
    augment=False,           # 是否启用数据增强
    cache=False,             # 是否缓存图像
    pad=0.0,                 # 填充参数
    rect=False,              # 是否使用矩形训练
    rank=-1,                 # 分布式训练排名
    workers=8,               # 数据加载工作进程数
    image_weights=False,     # 是否使用图像权重
    quad=False,              # 是否使用四倍批次大小
    prefix="",               # 日志前缀
    shuffle=False,           # 是否打乱数据
    mask_downsample_ratio=1, # 掩码下采样比例
    overlap_mask=False,      # 是否允许掩码重叠
    seed=0,                  # 随机种子
):
    """创建用于训练、验证或测试YOLO模型的数据加载器，支持各种数据集选项。"""
    # 矩形训练与数据打乱不兼容
    if rect and shuffle:
        # 矩形训练与数据打乱不兼容
        LOGGER.warning("WARNING ⚠️ --rect is incompatible with DataLoader shuffle, setting shuffle=False")
        shuffle = False
    
    # 在分布式训练中，只在第一个进程中初始化数据集缓存
    with torch_distributed_zero_first(rank):
        dataset = LoadImagesAndLabelsAndMasks(
            path,
            imgsz,
            batch_size,
            augment=augment,              # 数据增强
            hyp=hyp,                      # 超参数
            rect=rect,                    # 矩形批次
            cache_images=cache,           # 图像缓存
            single_cls=single_cls,        # 单类别
            stride=int(stride),           # 步长
            pad=pad,                      # 填充
            image_weights=image_weights,  # 图像权重
            prefix=prefix,                # 前缀
            downsample_ratio=mask_downsample_ratio,  # 掩码下采样比例
            overlap=overlap_mask,         # 掩码重叠
            rank=rank,                    # 进程排名
        )

    # 确保批次大小不超过数据集大小
    batch_size = min(batch_size, len(dataset))
    
    # 计算CUDA设备数量和工作进程数
    nd = torch.cuda.device_count()  # CUDA设备数量
    nw = min([os.cpu_count() // max(nd, 1), batch_size if batch_size > 1 else 0, workers])  # 工作进程数
    
    # 设置采样器（分布式训练时使用智能分布式采样器）
    sampler = None if rank == -1 else SmartDistributedSampler(dataset, shuffle=shuffle)
    
    # 选择数据加载器类型（图像权重时只能使用DataLoader）
    loader = DataLoader if image_weights else InfiniteDataLoader
    
    # 设置随机数生成器
    generator = torch.Generator()
    generator.manual_seed(6148914691236517205 + seed + RANK)
    
    # 创建并返回数据加载器和数据集
    return loader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle and sampler is None,  # 只有在没有采样器时才打乱
        num_workers=nw,                       # 工作进程数
        sampler=sampler,                      # 采样器
        drop_last=quad,                       # 四倍批次时丢弃最后一个不完整批次
        pin_memory=True,                      # 固定内存以加速GPU传输
        collate_fn=LoadImagesAndLabelsAndMasks.collate_fn4 if quad else LoadImagesAndLabelsAndMasks.collate_fn,
        worker_init_fn=seed_worker,           # 工作进程初始化函数
        generator=generator,                  # 随机数生成器
    ), dataset


class LoadImagesAndLabelsAndMasks(LoadImagesAndLabels):  # 用于训练/测试
    """加载图像、标签和分割掩码，用于训练和测试YOLO模型，支持数据增强。"""

    def __init__(
        self,
        path,                # 数据集路径
        img_size=640,        # 图像尺寸
        batch_size=16,       # 批次大小
        augment=False,       # 是否启用数据增强
        hyp=None,            # 超参数字典
        rect=False,          # 是否使用矩形训练
        image_weights=False, # 是否使用图像权重
        cache_images=False,  # 是否缓存图像
        single_cls=False,    # 是否为单类别检测
        stride=32,           # 模型步长
        pad=0,               # 填充参数
        min_items=0,         # 最小项目数
        prefix="",           # 日志前缀
        downsample_ratio=1,  # 下采样比例
        overlap=False,       # 是否允许重叠
        rank=-1,             # 分布式训练排名
        seed=0,              # 随机种子
    ):
        """初始化数据集，具备图像、标签和掩码加载功能，用于训练/测试。"""
        # 调用父类初始化方法
        super().__init__(
            path,
            img_size,
            batch_size,
            augment,
            hyp,
            rect,
            image_weights,
            cache_images,
            single_cls,
            stride,
            pad,
            min_items,
            prefix,
            rank,
            seed,
        )
        # 设置分割特有的属性
        self.downsample_ratio = downsample_ratio  # 掩码下采样比例
        self.overlap = overlap                    # 是否允许掩码重叠

    def __getitem__(self, index):
        """返回指定索引处数据集的变换项，处理索引和图像权重。"""
        index = self.indices[index]  # 线性、打乱或图像权重索引

        hyp = self.hyp
        # 根据概率决定是否使用马赛克增强
        if mosaic := self.mosaic and random.random() < hyp["mosaic"]:
            # 加载马赛克图像
            img, labels, segments = self.load_mosaic(index)
            shapes = None

            # MixUp数据增强
            if random.random() < hyp["mixup"]:
                img, labels, segments = mixup(img, labels, segments, *self.load_mosaic(random.randint(0, self.n - 1)))

        else:
            # 加载单张图像
            img, (h0, w0), (h, w) = self.load_image(index)

            # 应用letterbox变换（保持宽高比的缩放和填充）
            shape = self.batch_shapes[self.batch[index]] if self.rect else self.img_size  # 最终letterbox形状
            img, ratio, pad = letterbox(img, shape, auto=False, scaleup=self.augment)
            shapes = (h0, w0), ((h / h0, w / w0), pad)  # 用于COCO mAP重新缩放

            # 复制标签和分割数据
            labels = self.labels[index].copy()
            # 分割多边形数据：[array, array, ....], array.shape=(num_points, 2), xyxyxyxy格式
            segments = self.segments[index].copy()
            
            # 将分割多边形坐标从归一化转换为像素坐标
            if len(segments):
                for i_s in range(len(segments)):
                    segments[i_s] = xyn2xy(
                        segments[i_s],
                        ratio[0] * w,
                        ratio[1] * h,
                        padw=pad[0],
                        padh=pad[1],
                    )
            
            # 将标签从归一化xywh格式转换为像素xyxy格式
            if labels.size:
                labels[:, 1:] = xywhn2xyxy(labels[:, 1:], ratio[0] * w, ratio[1] * h, padw=pad[0], padh=pad[1])

            # 应用随机透视变换增强
            if self.augment:
                img, labels, segments = random_perspective(
                    img,
                    labels,
                    segments=segments,
                    degrees=hyp["degrees"],      # 旋转角度
                    translate=hyp["translate"],  # 平移
                    scale=hyp["scale"],          # 缩放
                    shear=hyp["shear"],          # 剪切
                    perspective=hyp["perspective"],  # 透视变换
                )

        nl = len(labels)  # 标签数量
        masks = []
        
        # 处理分割掩码
        if nl:
            # 将边界框从像素xyxy格式转换为归一化xywh格式
            labels[:, 1:5] = xyxy2xywhn(labels[:, 1:5], w=img.shape[1], h=img.shape[0], clip=True, eps=1e-3)
            
            if self.overlap:
                # 处理重叠掩码
                masks, sorted_idx = polygons2masks_overlap(
                    img.shape[:2], segments, downsample_ratio=self.downsample_ratio
                )
                masks = masks[None]  # (640, 640) -> (1, 640, 640)
                labels = labels[sorted_idx]  # 按面积排序标签
            else:
                # 处理非重叠掩码
                masks = polygons2masks(img.shape[:2], segments, color=1, downsample_ratio=self.downsample_ratio)

        # 转换掩码为张量格式
        masks = (
            torch.from_numpy(masks)
            if len(masks)
            else torch.zeros(
                1 if self.overlap else nl, 
                img.shape[0] // self.downsample_ratio, 
                img.shape[1] // self.downsample_ratio
            )
        )
        # TODO: albumentations支持
        if self.augment:
            # Albumentations数据增强
            # 有些增强不会改变边界框和掩码，
            # 所以暂时保持原样
            img, labels = self.albumentations(img, labels)
            nl = len(labels)  # 在albumentations后更新标签数量

            # HSV色彩空间增强
            augment_hsv(img, hgain=hyp["hsv_h"], sgain=hyp["hsv_s"], vgain=hyp["hsv_v"])

            # 上下翻转
            if random.random() < hyp["flipud"]:
                img = np.flipud(img)
                if nl:
                    labels[:, 2] = 1 - labels[:, 2]  # 调整y坐标
                    masks = torch.flip(masks, dims=[1])  # 翻转掩码

            # 左右翻转
            if random.random() < hyp["fliplr"]:
                img = np.fliplr(img)
                if nl:
                    labels[:, 1] = 1 - labels[:, 1]  # 调整x坐标
                    masks = torch.flip(masks, dims=[2])  # 翻转掩码

            # 裁剪增强  # labels = cutout(img, labels, p=0.5)

        # 准备输出标签
        labels_out = torch.zeros((nl, 6))
        if nl:
            labels_out[:, 1:] = torch.from_numpy(labels)

        # 转换图像格式
        img = img.transpose((2, 0, 1))[::-1]  # HWC转CHW，BGR转RGB
        img = np.ascontiguousarray(img)  # 确保内存连续性

        return (torch.from_numpy(img), labels_out, self.im_files[index], shapes, masks)

    def load_mosaic(self, index):
        """加载1张图像+3张随机图像组成4图像YOLOv5马赛克，相应调整标签和分割。"""
        labels4, segments4 = [], []
        s = self.img_size
        yc, xc = (int(random.uniform(-x, 2 * s + x)) for x in self.mosaic_border)  # 马赛克中心点x, y

        # 3个额外的图像索引
        indices = [index] + random.choices(self.indices, k=3)  # 3个额外的图像索引
        for i, index in enumerate(indices):
            # 加载图像
            img, _, (h, w) = self.load_image(index)

            # 将图像放置到img4中
            if i == 0:  # 左上角
                img4 = np.full((s * 2, s * 2, img.shape[2]), 114, dtype=np.uint8)  # 4个瓦片的基础图像
                x1a, y1a, x2a, y2a = max(xc - w, 0), max(yc - h, 0), xc, yc  # xmin, ymin, xmax, ymax (大图像)
                x1b, y1b, x2b, y2b = w - (x2a - x1a), h - (y2a - y1a), w, h  # xmin, ymin, xmax, ymax (小图像)
            elif i == 1:  # 右上角
                x1a, y1a, x2a, y2a = xc, max(yc - h, 0), min(xc + w, s * 2), yc
                x1b, y1b, x2b, y2b = 0, h - (y2a - y1a), min(w, x2a - x1a), h
            elif i == 2:  # 左下角
                x1a, y1a, x2a, y2a = max(xc - w, 0), yc, xc, min(s * 2, yc + h)
                x1b, y1b, x2b, y2b = w - (x2a - x1a), 0, w, min(y2a - y1a, h)
            elif i == 3:  # 右下角
                x1a, y1a, x2a, y2a = xc, yc, min(xc + w, s * 2), min(s * 2, yc + h)
                x1b, y1b, x2b, y2b = 0, 0, min(w, x2a - x1a), min(y2a - y1a, h)

            img4[y1a:y2a, x1a:x2a] = img[y1b:y2b, x1b:x2b]  # img4[ymin:ymax, xmin:xmax]
            padw = x1a - x1b  # 水平填充
            padh = y1a - y1b  # 垂直填充

            labels, segments = self.labels[index].copy(), self.segments[index].copy()

            if labels.size:
                labels[:, 1:] = xywhn2xyxy(labels[:, 1:], w, h, padw, padh)  # 归一化xywh转像素xyxy格式
                segments = [xyn2xy(x, w, h, padw, padh) for x in segments]
            labels4.append(labels)
            segments4.extend(segments)

        # 连接/裁剪标签
        labels4 = np.concatenate(labels4, 0)
        for x in (labels4[:, 1:], *segments4):
            np.clip(x, 0, 2 * s, out=x)  # 使用random_perspective()时进行裁剪
        # img4, labels4 = replicate(img4, labels4)  # 复制

        # 数据增强
        img4, labels4, segments4 = copy_paste(img4, labels4, segments4, p=self.hyp["copy_paste"])
        img4, labels4, segments4 = random_perspective(
            img4,
            labels4,
            segments4,
            degrees=self.hyp["degrees"],
            translate=self.hyp["translate"],
            scale=self.hyp["scale"],
            shear=self.hyp["shear"],
            perspective=self.hyp["perspective"],
            border=self.mosaic_border,
        )  # border to remove
        return img4, labels4, segments4

    @staticmethod
    def collate_fn(batch):
        """DataLoader的自定义整理函数，批处理图像、标签、路径、形状和分割掩码。"""
        img, label, path, shapes, masks = zip(*batch)  # 转置
        batched_masks = torch.cat(masks, 0)  # 连接掩码
        for i, l in enumerate(label):
            l[:, 0] = i  # 为build_targets()添加目标图像索引
        return torch.stack(img, 0), torch.cat(label, 0), path, shapes, batched_masks


def polygon2mask(img_size, polygons, color=1, downsample_ratio=1):
    """
    将多边形转换为掩码。
    
    Args:
        img_size (tuple): 图像尺寸。
        polygons (np.ndarray): [N, M], N是多边形数量，
            M是点的数量(需要被2整除)。
        color (int): 填充颜色。
        downsample_ratio (int): 下采样比例。
    """
    mask = np.zeros(img_size, dtype=np.uint8)
    polygons = np.asarray(polygons)
    polygons = polygons.astype(np.int32)
    shape = polygons.shape
    polygons = polygons.reshape(shape[0], -1, 2)
    cv2.fillPoly(mask, polygons, color=color)  # 填充多边形
    nh, nw = (img_size[0] // downsample_ratio, img_size[1] // downsample_ratio)
    # 注意：先fillPoly再resize是为了保持与mask-ratio=1时
    # 损失计算的一致性
    mask = cv2.resize(mask, (nw, nh))
    return mask


def polygons2masks(img_size, polygons, color, downsample_ratio=1):
    """
    将多个多边形转换为掩码数组。
    
    Args:
        img_size (tuple): 图像尺寸。
        polygons (list[np.ndarray]): 每个多边形是[N, M]，
            N是多边形数量，
            M是点的数量(需要被2整除)。
        color (int): 填充颜色。
        downsample_ratio (int): 下采样比例。
    """
    masks = []
    for si in range(len(polygons)):
        mask = polygon2mask(img_size, [polygons[si].reshape(-1)], color, downsample_ratio)
        masks.append(mask)
    return np.array(masks)


def polygons2masks_overlap(img_size, segments, downsample_ratio=1):
    """返回一个重叠掩码，按面积大小排序处理重叠区域。"""
    masks = np.zeros(
        (img_size[0] // downsample_ratio, img_size[1] // downsample_ratio),
        dtype=np.int32 if len(segments) > 255 else np.uint8,
    )
    areas = []  # 存储每个分割区域的面积
    ms = []     # 存储每个掩码
    
    # 为每个分割生成掩码并计算面积
    for si in range(len(segments)):
        mask = polygon2mask(
            img_size,
            [segments[si].reshape(-1)],
            downsample_ratio=downsample_ratio,
            color=1,
        )
        ms.append(mask)
        areas.append(mask.sum())  # 计算掩码面积
    
    # 按面积从大到小排序
    areas = np.asarray(areas)
    index = np.argsort(-areas)  # 降序排列索引
    ms = np.array(ms)[index]
    
    # 按面积大小顺序叠加掩码，处理重叠
    for i in range(len(segments)):
        mask = ms[i] * (i + 1)  # 给每个掩码分配唯一ID
        masks = masks + mask
        masks = np.clip(masks, a_min=0, a_max=i + 1)  # 限制值范围
    
    return masks, index


class LoadDualModalImagesAndMasks(LoadImagesAndLabelsAndMasks):
    """加载双模态图像(PPL和XPL)、标签和分割掩码，用于多模态训练/测试。"""

    def __init__(
        self,
        path,                # PPL图像路径
        img_size=640,        # 图像尺寸
        batch_size=16,       # 批次大小
        augment=False,       # 是否启用数据增强
        hyp=None,            # 超参数字典
        rect=False,          # 是否使用矩形训练
        image_weights=False, # 是否使用图像权重
        cache_images=False,  # 是否缓存图像
        single_cls=False,    # 是否为单类别检测
        stride=32,           # 模型步长
        pad=0,               # 填充参数
        min_items=0,         # 最小项目数
        prefix="",           # 日志前缀
        downsample_ratio=1,  # 下采样比例
        overlap=False,       # 是否允许重叠
        rank=-1,             # 分布式训练排名
        seed=0,              # 随机种子
        xpl_path=None,       # XPL图像路径
    ):
        """初始化双模态数据集，具备PPL和XPL图像加载能力。"""
        # 首先初始化父类
        super().__init__(
            path,
            img_size,
            batch_size,
            augment,
            hyp,
            rect,
            image_weights,
            cache_images,
            single_cls,
            stride,
            pad,
            min_items,
            prefix,
            downsample_ratio,
            overlap,
            rank,
            seed,
        )
        
        # 双模态特定属性
        self.xpl_path = xpl_path
        self.ppl_files = self.im_files.copy()  # PPL图像(原始)
        self.xpl_files = []  # XPL图像
        self.modal_pairs = {}  # PPL到XPL文件的映射
        self.missing_xpl = []  # 跟踪缺失的XPL文件
        
        # 构建双模态文件对
        self._build_modal_pairs()
        
        LOGGER.info(f"{prefix}双模态数据集已初始化: {len(self.ppl_files)} PPL图像, "
                   f"{len(self.xpl_files)} XPL图像, {len(self.missing_xpl)} 缺失XPL文件")

    def _build_modal_pairs(self):
        """基于文件名匹配构建PPL和XPL图像的文件对。"""
        if not self.xpl_path:
            # 如果未提供XPL路径，使用PPL图像作为两种模态
            LOGGER.warning("XPL path not provided, using PPL images for both modalities")
            self.xpl_files = self.ppl_files.copy()
            self.modal_pairs = {ppl: ppl for ppl in self.ppl_files}
            return
            
        xpl_dir = Path(self.xpl_path)
        if not xpl_dir.exists():
            # 如果XPL目录不存在，使用PPL图像作为两种模态
            LOGGER.warning(f"XPL directory {xpl_dir} does not exist, using PPL images for both modalities")
            self.xpl_files = self.ppl_files.copy()
            self.modal_pairs = {ppl: ppl for ppl in self.ppl_files}
            return
            
        # 创建文件名到XPL路径的映射
        xpl_file_map = {}
        for ext in ['*.png', '*.jpg', '*.jpeg', '*.bmp', '*.tif', '*.tiff']:
            for xpl_file in xpl_dir.rglob(ext):
                stem = xpl_file.stem
                xpl_file_map[stem] = str(xpl_file)
                
        # 将PPL文件与XPL文件进行匹配
        for ppl_file in self.ppl_files:
            ppl_stem = Path(ppl_file).stem
            if ppl_stem in xpl_file_map:
                xpl_file = xpl_file_map[ppl_stem]
                self.xpl_files.append(xpl_file)
                self.modal_pairs[ppl_file] = xpl_file
            else:
                # 缺少XPL文件，使用PPL作为备用
                self.xpl_files.append(ppl_file)
                self.modal_pairs[ppl_file] = ppl_file
                self.missing_xpl.append(ppl_stem)
                
    def load_image(self, i):
        """在索引i处加载双模态图像（PPL和XPL）。"""
        # 加载PPL图像（原始方法）
        ppl_img, (h0, w0), (h, w) = super().load_image(i)
        
        # 加载XPL图像
        xpl_path = self.xpl_files[i]
        xpl_img = cv2.imread(xpl_path)  # BGR格式
        assert xpl_img is not None, f"XPL Image Not Found {xpl_path}"
        
        # 如果需要，调整XPL图像尺寸以匹配PPL图像
        if xpl_img.shape[:2] != (h, w):
            xpl_img = cv2.resize(xpl_img, (w, h), interpolation=cv2.INTER_LINEAR)
            
        return (ppl_img, xpl_img), (h0, w0), (h, w)
        
    def __getitem__(self, index):
        """返回指定索引处数据集的双模态变换项。"""
        index = self.indices[index]  # 线性、随机或图像权重索引

        hyp = self.hyp
        if mosaic := self.mosaic and random.random() < hyp["mosaic"]:
            # 为双模态加载马赛克
            (ppl_img, xpl_img), labels, segments = self.load_mosaic(index)
            shapes = None

            # 双模态的MixUp数据增强
            if random.random() < hyp["mixup"]:
                (ppl_img2, xpl_img2), labels2, segments2 = self.load_mosaic(random.randint(0, self.n - 1))
                ppl_img, labels, segments = mixup(ppl_img, labels, segments, ppl_img2, labels2, segments2)
                xpl_img, _, _ = mixup(xpl_img, labels, segments, xpl_img2, labels2, segments2)

        else:
            # 加载双模态图像
            (ppl_img, xpl_img), (h0, w0), (h, w) = self.load_image(index)

            # 使用相同参数对两个图像进行letterbox处理
            shape = self.batch_shapes[self.batch[index]] if self.rect else self.img_size
            ppl_img, ratio, pad = letterbox(ppl_img, shape, auto=False, scaleup=self.augment)
            xpl_img, _, _ = letterbox(xpl_img, shape, auto=False, scaleup=self.augment)
            shapes = (h0, w0), ((h / h0, w / w0), pad)

            labels = self.labels[index].copy()
            segments = self.segments[index].copy()
            if len(segments):
                for i_s in range(len(segments)):
                    segments[i_s] = xyn2xy(
                        segments[i_s],
                        ratio[0] * w,
                        ratio[1] * h,
                        padw=pad[0],
                        padh=pad[1],
                    )
            if labels.size:
                labels[:, 1:] = xywhn2xyxy(labels[:, 1:], ratio[0] * w, ratio[1] * h, padw=pad[0], padh=pad[1])

            if self.augment:
                # 对两个图像应用相同的随机透视变换
                ppl_img, labels, segments = random_perspective(
                    ppl_img,
                    labels,
                    segments=segments,
                    degrees=hyp["degrees"],
                    translate=hyp["translate"],
                    scale=hyp["scale"],
                    shear=hyp["shear"],
                    perspective=hyp["perspective"],
                )
                xpl_img, _, _ = random_perspective(
                    xpl_img,
                    labels,
                    segments=segments,
                    degrees=hyp["degrees"],
                    translate=hyp["translate"],
                    scale=hyp["scale"],
                    shear=hyp["shear"],
                    perspective=hyp["perspective"],
                )

        nl = len(labels)
        masks = []
        if nl:
            labels[:, 1:5] = xyxy2xywhn(labels[:, 1:5], w=ppl_img.shape[1], h=ppl_img.shape[0], clip=True, eps=1e-3)
            if self.overlap:
                masks, sorted_idx = polygons2masks_overlap(
                    ppl_img.shape[:2], segments, downsample_ratio=self.downsample_ratio
                )
                masks = masks[None]
                labels = labels[sorted_idx]
            else:
                masks = polygons2masks(ppl_img.shape[:2], segments, color=1, downsample_ratio=self.downsample_ratio)

        masks = (
            torch.from_numpy(masks)
            if len(masks)
            else torch.zeros(
                1 if self.overlap else nl, ppl_img.shape[0] // self.downsample_ratio, ppl_img.shape[1] // self.downsample_ratio
            )
        )

        if self.augment:
            # 对两个图像应用相同的增强
            ppl_img, labels = self.albumentations(ppl_img, labels)
            xpl_img, _ = self.albumentations(xpl_img, labels)
            nl = len(labels)

            # HSV颜色空间增强
            augment_hsv(ppl_img, hgain=hyp["hsv_h"], sgain=hyp["hsv_s"], vgain=hyp["hsv_v"])
            augment_hsv(xpl_img, hgain=hyp["hsv_h"], sgain=hyp["hsv_s"], vgain=hyp["hsv_v"])

            # 上下翻转
            if random.random() < hyp["flipud"]:
                ppl_img = np.flipud(ppl_img)
                xpl_img = np.flipud(xpl_img)
                if nl:
                    labels[:, 2] = 1 - labels[:, 2]
                    masks = torch.flip(masks, dims=[1])

            # 左右翻转
            if random.random() < hyp["fliplr"]:
                ppl_img = np.fliplr(ppl_img)
                xpl_img = np.fliplr(xpl_img)
                if nl:
                    labels[:, 1] = 1 - labels[:, 1]
                    masks = torch.flip(masks, dims=[2])

        labels_out = torch.zeros((nl, 6))
        if nl:
            labels_out[:, 1:] = torch.from_numpy(labels)

        # 转换两个图像格式
        ppl_img = ppl_img.transpose((2, 0, 1))[::-1]  # HWC转CHW，BGR转RGB
        ppl_img = np.ascontiguousarray(ppl_img)
        xpl_img = xpl_img.transpose((2, 0, 1))[::-1]  # HWC转CHW，BGR转RGB
        xpl_img = np.ascontiguousarray(xpl_img)

        return (torch.from_numpy(ppl_img), torch.from_numpy(xpl_img), labels_out, self.im_files[index], shapes, masks)
        
    def load_mosaic(self, index):
        """为双模态图像加载4图像马赛克。"""
        labels4, segments4 = [], []
        s = self.img_size
        yc, xc = (int(random.uniform(-x, 2 * s + x)) for x in self.mosaic_border)

        indices = [index] + random.choices(self.indices, k=3)
        for i, index in enumerate(indices):
            # 加载双模态图像
            (ppl_img, xpl_img), _, (h, w) = self.load_image(index)

            # 在马赛克中放置图像
            if i == 0:  # 左上角
                ppl_img4 = np.full((s * 2, s * 2, ppl_img.shape[2]), 114, dtype=np.uint8)
                xpl_img4 = np.full((s * 2, s * 2, xpl_img.shape[2]), 114, dtype=np.uint8)
                x1a, y1a, x2a, y2a = max(xc - w, 0), max(yc - h, 0), xc, yc
                x1b, y1b, x2b, y2b = w - (x2a - x1a), h - (y2a - y1a), w, h
            elif i == 1:  # 右上角
                x1a, y1a, x2a, y2a = xc, max(yc - h, 0), min(xc + w, s * 2), yc
                x1b, y1b, x2b, y2b = 0, h - (y2a - y1a), min(w, x2a - x1a), h
            elif i == 2:  # 左下角
                x1a, y1a, x2a, y2a = max(xc - w, 0), yc, xc, min(s * 2, yc + h)
                x1b, y1b, x2b, y2b = w - (x2a - x1a), 0, w, min(y2a - y1a, h)
            elif i == 3:  # 右下角
                x1a, y1a, x2a, y2a = xc, yc, min(xc + w, s * 2), min(s * 2, yc + h)
                x1b, y1b, x2b, y2b = 0, 0, min(w, x2a - x1a), min(y2a - y1a, h)

            ppl_img4[y1a:y2a, x1a:x2a] = ppl_img[y1b:y2b, x1b:x2b]
            xpl_img4[y1a:y2a, x1a:x2a] = xpl_img[y1b:y2b, x1b:x2b]
            padw = x1a - x1b
            padh = y1a - y1b

            labels, segments = self.labels[index].copy(), self.segments[index].copy()
            if labels.size:
                labels[:, 1:] = xywhn2xyxy(labels[:, 1:], w, h, padw, padh)
                segments = [xyn2xy(x, w, h, padw, padh) for x in segments]
            labels4.append(labels)
            segments4.extend(segments)

        # 连接/裁剪标签
        labels4 = np.concatenate(labels4, 0)
        for x in (labels4[:, 1:], *segments4):
            np.clip(x, 0, 2 * s, out=x)

        # 数据增强
        ppl_img4, labels4, segments4 = copy_paste(ppl_img4, labels4, segments4, p=self.hyp["copy_paste"])
        xpl_img4, _, _ = copy_paste(xpl_img4, labels4, segments4, p=self.hyp["copy_paste"])
        ppl_img4, labels4, segments4 = random_perspective(
            ppl_img4,
            labels4,
            segments4,
            degrees=self.hyp["degrees"],
            translate=self.hyp["translate"],
            scale=self.hyp["scale"],
            shear=self.hyp["shear"],
            perspective=self.hyp["perspective"],
            border=self.mosaic_border,
        )
        xpl_img4, _, _ = random_perspective(
            xpl_img4,
            labels4,
            segments4,
            degrees=self.hyp["degrees"],
            translate=self.hyp["translate"],
            scale=self.hyp["scale"],
            shear=self.hyp["shear"],
            perspective=self.hyp["perspective"],
            border=self.mosaic_border,
        )
        return (ppl_img4, xpl_img4), labels4, segments4
        
    @staticmethod
    def collate_fn(batch):
        """双模态DataLoader的自定义整理函数。"""
        ppl_img, xpl_img, label, path, shapes, masks = zip(*batch)
        batched_masks = torch.cat(masks, 0)
        for i, l in enumerate(label):
            l[:, 0] = i
        return torch.stack(ppl_img, 0), torch.stack(xpl_img, 0), torch.cat(label, 0), path, shapes, batched_masks
        
    @staticmethod
    def collate_fn4(batch):
        """双模态DataLoader的四倍批处理自定义整理函数。"""
        ppl_img, xpl_img, label, path, shapes, masks = zip(*batch)
        n = len(shapes) // 4
        ppl_img4, xpl_img4, label4, path4, shapes4 = [], [], [], path[:n], shapes[:n]
        ho = torch.tensor([[0.0, 0, 0, 1, 0, 0]])
        for i in range(n):
            i *= 4
            if random.random() < 0.5:
                im1 = F.interpolate(ppl_img[i].unsqueeze(0).float(), scale_factor=2.0, mode="bilinear", align_corners=False)[
                    0
                ].type(ppl_img[i].type())
                im2 = F.interpolate(xpl_img[i].unsqueeze(0).float(), scale_factor=2.0, mode="bilinear", align_corners=False)[
                    0
                ].type(xpl_img[i].type())
                l = label[i]
            else:
                im1 = torch.cat((torch.cat((ppl_img[i], ppl_img[i + 1]), 1), torch.cat((ppl_img[i + 2], ppl_img[i + 3]), 1)), 2)
                im2 = torch.cat((torch.cat((xpl_img[i], xpl_img[i + 1]), 1), torch.cat((xpl_img[i + 2], xpl_img[i + 3]), 1)), 2)
                l = torch.cat((label[i], label[i + 1] + ho, label[i + 2] + ho, label[i + 3] + ho), 0)
            ppl_img4.append(im1)
            xpl_img4.append(im2)
            label4.append(l)

        for i, l in enumerate(label4):
            l[:, 0] = i
        return torch.stack(ppl_img4, 0), torch.stack(xpl_img4, 0), torch.cat(label4, 0), path4, shapes4, torch.cat(masks, 0)


def create_multimodal_dataloader(
    path,
    imgsz,
    batch_size,
    stride,
    single_cls=False,
    hyp=None,
    augment=False,
    cache=False,
    pad=0.0,
    rect=False,
    rank=-1,
    workers=8,
    image_weights=False,
    quad=False,
    prefix="",
    shuffle=False,
    mask_downsample_ratio=1,
    overlap_mask=False,
    seed=0,
    xpl_path=None,
    multimodal=False,
):
    """创建支持双模态的多模态训练/测试数据加载器。"""
    if rect and shuffle:
        LOGGER.warning("WARNING ⚠️ --rect is incompatible with DataLoader shuffle, setting shuffle=False")
        shuffle = False
        
    with torch_distributed_zero_first(rank):
        if multimodal and xpl_path:
            # 创建双模态数据集
            dataset = LoadDualModalImagesAndMasks(
                path,
                imgsz,
                batch_size,
                augment=augment,
                hyp=hyp,
                rect=rect,
                cache_images=cache,
                single_cls=single_cls,
                stride=int(stride),
                pad=pad,
                image_weights=image_weights,
                prefix=prefix,
                downsample_ratio=mask_downsample_ratio,
                overlap=overlap_mask,
                rank=rank,
                xpl_path=xpl_path,
            )
        else:
            # 创建单模态数据集
            dataset = LoadImagesAndLabelsAndMasks(
                path,
                imgsz,
                batch_size,
                augment=augment,
                hyp=hyp,
                rect=rect,
                cache_images=cache,
                single_cls=single_cls,
                stride=int(stride),
                pad=pad,
                image_weights=image_weights,
                prefix=prefix,
                downsample_ratio=mask_downsample_ratio,
                overlap=overlap_mask,
                rank=rank,
            )

    batch_size = min(batch_size, len(dataset))
    nd = torch.cuda.device_count()
    nw = min([os.cpu_count() // max(nd, 1), batch_size if batch_size > 1 else 0, workers])
    sampler = None if rank == -1 else SmartDistributedSampler(dataset, shuffle=shuffle)
    loader = DataLoader if image_weights else InfiniteDataLoader
    generator = torch.Generator()
    generator.manual_seed(6148914691236517205 + seed + RANK)
    
    # 使用适当的整理函数
    if multimodal and xpl_path:
        collate_fn = LoadDualModalImagesAndMasks.collate_fn4 if quad else LoadDualModalImagesAndMasks.collate_fn
    else:
        collate_fn = LoadImagesAndLabelsAndMasks.collate_fn4 if quad else LoadImagesAndLabelsAndMasks.collate_fn
        
    return loader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle and sampler is None,
        num_workers=nw,
        sampler=sampler,
        drop_last=quad,
        pin_memory=True,
        collate_fn=collate_fn,
        worker_init_fn=seed_worker,
        generator=generator,
    ), dataset
