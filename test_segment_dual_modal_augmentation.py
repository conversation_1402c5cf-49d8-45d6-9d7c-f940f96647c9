#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分割双模态数据增强一致性测试脚本

测试 utils/segment/augmentations.py 中的双模态增强函数：
- mixup: 双模态图像混合
- random_perspective: 双模态几何变换

验证PPL和XPL图像在增强过程中的空间一致性
"""

import os
import sys
import cv2
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

# 添加项目根目录到路径
FILE = Path(__file__).resolve()
ROOT = FILE.parents[0]  # YOLOv5 root directory
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))  # add ROOT to PATH

# 导入分割增强函数
from utils.segment.augmentations import mixup, random_perspective


def create_test_images(size=(640, 640)):
    """
    创建测试用的PPL和XPL图像
    
    Args:
        size: 图像尺寸 (height, width)
    
    Returns:
        ppl_img, xpl_img: PPL和XPL测试图像
    """
    h, w = size
    
    # 创建PPL图像 (模拟单偏光)
    ppl_img = np.zeros((h, w, 3), dtype=np.uint8)
    # 添加一些几何图案
    cv2.rectangle(ppl_img, (100, 100), (300, 300), (255, 0, 0), -1)  # 红色矩形
    cv2.circle(ppl_img, (400, 200), 80, (0, 255, 0), -1)  # 绿色圆形
    cv2.ellipse(ppl_img, (500, 400), (60, 40), 45, 0, 360, (0, 0, 255), -1)  # 蓝色椭圆
    
    # 创建XPL图像 (模拟正交光，与PPL有相关性但不完全相同)
    xpl_img = np.zeros((h, w, 3), dtype=np.uint8)
    # 相同位置但不同颜色的图案
    cv2.rectangle(xpl_img, (100, 100), (300, 300), (128, 255, 128), -1)  # 浅绿色矩形
    cv2.circle(xpl_img, (400, 200), 80, (255, 128, 0), -1)  # 橙色圆形
    cv2.ellipse(xpl_img, (500, 400), (60, 40), 45, 0, 360, (255, 0, 255), -1)  # 紫色椭圆
    
    return ppl_img, xpl_img


def create_test_labels_and_segments():
    """
    创建测试用的标签和分割信息
    
    Returns:
        labels: 目标框标签 [cls, x1, y1, x2, y2] (归一化)
        segments: 分割多边形点
    """
    # 创建一些测试标签 (归一化坐标)
    labels = np.array([
        [0, 0.15625, 0.15625, 0.46875, 0.46875],  # 矩形框
        [1, 0.625, 0.3125, 0.75, 0.4375],        # 圆形框
        [2, 0.78125, 0.625, 0.90625, 0.75]       # 椭圆框
    ])
    
    # 创建对应的分割多边形 (归一化坐标) - 使用列表格式
    segments = [
        # 矩形的四个角点
        np.array([[0.15625, 0.15625], [0.46875, 0.15625], [0.46875, 0.46875], [0.15625, 0.46875]], dtype=np.float32),
        # 圆形的近似多边形
        np.array([[0.625, 0.3125], [0.7, 0.35], [0.7, 0.42], [0.625, 0.4375]], dtype=np.float32),
        # 椭圆的近似多边形
        np.array([[0.78125, 0.625], [0.9, 0.68], [0.85, 0.73], [0.78125, 0.75]], dtype=np.float32)
    ]
    
    return labels, segments


def calculate_consistency_score(img1, img2, threshold=10):
    """
    计算两张图像的一致性分数
    
    Args:
        img1, img2: 要比较的图像
        threshold: 像素差异阈值
    
    Returns:
        consistency_score: 一致性分数 (0-1)
    """
    if img1.shape != img2.shape:
        return 0.0
    
    # 计算像素级差异
    diff = np.abs(img1.astype(np.float32) - img2.astype(np.float32))
    diff_mean = np.mean(diff)
    
    # 计算结构相似性 (简化版)
    consistent_pixels = np.sum(diff < threshold)
    total_pixels = img1.size
    consistency = consistent_pixels / total_pixels
    
    return consistency


def test_mixup():
    """
    测试mixup函数的双模态一致性
    """
    print("\n=== 测试 mixup 双模态一致性 ===")
    
    # 创建测试数据
    ppl1, xpl1 = create_test_images()
    ppl2, xpl2 = create_test_images((640, 640))
    # 让第二组图像稍有不同
    ppl2 = cv2.flip(ppl2, 1)  # 水平翻转
    xpl2 = cv2.flip(xpl2, 1)
    
    labels1, segments1 = create_test_labels_and_segments()
    labels2, segments2 = create_test_labels_and_segments()
    
    try:
        # 测试双模态mixup
        result_ppl, result_labels, result_segments, result_xpl = mixup(
            ppl1, labels1, segments1, ppl2, labels2, segments2, xpl1, xpl2
        )
        
        print(f"✓ mixup双模态测试通过")
        print(f"  - PPL结果形状: {result_ppl.shape}")
        print(f"  - XPL结果形状: {result_xpl.shape}")
        print(f"  - 标签数量: {len(result_labels)}")
        print(f"  - 分割数量: {len(result_segments)}")
        
        return True, result_ppl, result_xpl
        
    except Exception as e:
        print(f"✗ mixup双模态测试失败: {e}")
        return False, None, None


def test_random_perspective():
    """
    测试random_perspective函数的双模态一致性
    """
    print("\n=== 测试 random_perspective 双模态一致性 ===")
    
    # 创建测试数据
    ppl_img, xpl_img = create_test_images()
    labels, segments = create_test_labels_and_segments()
    
    try:
        # 测试双模态透视变换
        result_ppl, result_labels, result_segments, result_xpl = random_perspective(
            ppl_img, labels, segments, 
            degrees=15, translate=0.1, scale=0.1, shear=10, perspective=0.0,
            im_xpl=xpl_img
        )
        
        print(f"✓ random_perspective双模态测试通过")
        print(f"  - PPL结果形状: {result_ppl.shape}")
        print(f"  - XPL结果形状: {result_xpl.shape}")
        print(f"  - 标签数量: {len(result_labels)}")
        print(f"  - 分割数量: {len(result_segments)}")
        
        # 计算空间一致性
        consistency = calculate_consistency_score(result_ppl, result_xpl)
        print(f"  - 空间一致性分数: {consistency:.3f}")
        
        return True, result_ppl, result_xpl, consistency
        
    except Exception as e:
        print(f"✗ random_perspective双模态测试失败: {e}")
        return False, None, None, 0.0


def visualize_results(original_ppl, original_xpl, aug_ppl, aug_xpl, method_name, save_dir="test_segment_results"):
    """
    可视化增强结果
    
    Args:
        original_ppl, original_xpl: 原始PPL和XPL图像
        aug_ppl, aug_xpl: 增强后的PPL和XPL图像
        method_name: 增强方法名称
        save_dir: 保存目录
    """
    os.makedirs(save_dir, exist_ok=True)
    
    # 创建对比图
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    fig.suptitle(f'分割双模态增强结果对比 - {method_name}', fontsize=16)
    
    # 原始PPL
    axes[0, 0].imshow(cv2.cvtColor(original_ppl, cv2.COLOR_BGR2RGB))
    axes[0, 0].set_title('原始PPL图像')
    axes[0, 0].axis('off')
    
    # 原始XPL
    axes[0, 1].imshow(cv2.cvtColor(original_xpl, cv2.COLOR_BGR2RGB))
    axes[0, 1].set_title('原始XPL图像')
    axes[0, 1].axis('off')
    
    # 增强后PPL
    axes[1, 0].imshow(cv2.cvtColor(aug_ppl, cv2.COLOR_BGR2RGB))
    axes[1, 0].set_title(f'增强后PPL图像 ({method_name})')
    axes[1, 0].axis('off')
    
    # 增强后XPL
    axes[1, 1].imshow(cv2.cvtColor(aug_xpl, cv2.COLOR_BGR2RGB))
    axes[1, 1].set_title(f'增强后XPL图像 ({method_name})')
    axes[1, 1].axis('off')
    
    plt.tight_layout()
    plt.savefig(f'{save_dir}/{method_name}_comparison.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    # 保存单独的图像
    cv2.imwrite(f'{save_dir}/{method_name}_ppl_original.png', original_ppl)
    cv2.imwrite(f'{save_dir}/{method_name}_xpl_original.png', original_xpl)
    cv2.imwrite(f'{save_dir}/{method_name}_ppl_augmented.png', aug_ppl)
    cv2.imwrite(f'{save_dir}/{method_name}_xpl_augmented.png', aug_xpl)


def main():
    """
    主测试函数
    """
    print("开始分割双模态数据增强一致性测试...")
    
    # 创建原始测试图像
    original_ppl, original_xpl = create_test_images()
    
    results = {}
    
    # 测试mixup
    success, aug_ppl, aug_xpl = test_mixup()
    if success:
        results['mixup'] = {'success': True, 'consistency': 1.0}  # mixup本身就是混合，一致性为1
        visualize_results(original_ppl, original_xpl, aug_ppl, aug_xpl, 'mixup')
    else:
        results['mixup'] = {'success': False, 'consistency': 0.0}
    
    # 测试random_perspective
    success, aug_ppl, aug_xpl, consistency = test_random_perspective()
    if success:
        results['random_perspective'] = {'success': True, 'consistency': consistency}
        visualize_results(original_ppl, original_xpl, aug_ppl, aug_xpl, 'random_perspective')
    else:
        results['random_perspective'] = {'success': False, 'consistency': 0.0}
    
    # 输出测试总结
    print("\n" + "="*50)
    print("分割双模态增强测试总结")
    print("="*50)
    
    total_tests = len(results)
    passed_tests = sum(1 for r in results.values() if r['success'])
    avg_consistency = np.mean([r['consistency'] for r in results.values()])
    
    for method, result in results.items():
        status = "✓ 通过" if result['success'] else "✗ 失败"
        print(f"{method:20s}: {status} (一致性: {result['consistency']:.3f})")
    
    print(f"\n总体结果: {passed_tests}/{total_tests} 测试通过")
    print(f"平均一致性分数: {avg_consistency:.3f}")
    
    if avg_consistency > 0.8:
        print("\n🎉 分割双模态增强一致性测试表现优秀！")
    elif avg_consistency > 0.6:
        print("\n✅ 分割双模态增强一致性测试表现良好")
    else:
        print("\n⚠️  分割双模态增强一致性需要进一步优化")
    
    print(f"\n可视化结果已保存到 test_segment_results/ 目录")


if __name__ == '__main__':
    main()