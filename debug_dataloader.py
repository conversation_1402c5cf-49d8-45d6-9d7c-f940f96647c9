import sys
sys.path.append('.')

from pathlib import Path
import yaml

# 模拟 segment/train.py 中的逻辑
with open('dataset-seg/datasets.yaml', 'r', encoding='utf-8') as f:
    data_dict = yaml.safe_load(f)

opt_multimodal = True  # 从命令行参数

print('opt.multimodal:', opt_multimodal)
print('data_dict.get("multimodal", False):', data_dict.get('multimodal', False))
print('Condition result:', opt_multimodal and data_dict.get('multimodal', False))

train_xpl_path = data_dict.get('train_xpl', '')
if train_xpl_path:
    train_xpl_path = str(Path(data_dict['path']) / train_xpl_path)

print('train_xpl_path:', repr(train_xpl_path))
print('bool(train_xpl_path):', bool(train_xpl_path))

# 模拟 create_multimodal_dataloader 中的条件
multimodal = True
xpl_path = train_xpl_path
print('multimodal and xpl_path:', multimodal and xpl_path)
print('Will create dual modal dataset:', multimodal and xpl_path)