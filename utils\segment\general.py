import cv2
import numpy as np
import torch
import torch.nn.functional as F


def crop_mask(masks, boxes):
    """
    裁剪 mask，只保留预测框范围内的区域，其余置零。
    Args:
        masks: [n, h, w] tensor，n个mask
        boxes: [n, 4] tensor，bbox坐标 (x1,y1,x2,y2)，相对坐标
    """
    n, h, w = masks.shape
    x1, y1, x2, y2 = torch.chunk(boxes[:, :, None], 4, 1)  # 拆成四个坐标
    r = torch.arange(w, device=masks.device, dtype=x1.dtype)[None, None, :]  # 宽度坐标
    c = torch.arange(h, device=masks.device, dtype=x1.dtype)[None, :, None]  # 高度坐标
    return masks * ((r >= x1) * (r < x2) * (c >= y1) * (c < y2))  # 裁剪区域


def process_mask_upsample(protos, masks_in, bboxes, shape):
    """
    先上采样，再裁剪 mask。
    Args:
        protos: [mask_dim, mask_h, mask_w] 掩码原型
        masks_in: [n, mask_dim] NMS 后的掩码系数
        bboxes: [n, 4] 对应的预测框
        shape: 输入图像尺寸 (h, w)
    Return: 上采样后的二值mask [n,h,w]
    """
    c, mh, mw = protos.shape
    masks = (masks_in @ protos.float().view(c, -1)).sigmoid().view(-1, mh, mw)
    masks = F.interpolate(masks[None], shape, mode="bilinear", align_corners=False)[0]
    masks = crop_mask(masks, bboxes)
    return masks.gt_(0.5)  # 二值化


def process_mask(protos, masks_in, bboxes, shape, upsample=False):
    """
    先裁剪，再上采样 mask。
    Args:
        protos: [mask_dim, mask_h, mask_w]
        masks_in: [n, mask_dim]
        bboxes: [n, 4]
        shape: 输入图像大小 (h,w)
        upsample: 是否在最后上采样
    """
    c, mh, mw = protos.shape
    ih, iw = shape
    masks = (masks_in @ protos.float().view(c, -1)).sigmoid().view(-1, mh, mw)

    # 将预测框缩放到特征图大小
    downsampled_bboxes = bboxes.clone()
    downsampled_bboxes[:, 0] *= mw / iw
    downsampled_bboxes[:, 2] *= mw / iw
    downsampled_bboxes[:, 3] *= mh / ih
    downsampled_bboxes[:, 1] *= mh / ih

    masks = crop_mask(masks, downsampled_bboxes)
    if upsample:  # 上采样到输入图像大小
        masks = F.interpolate(masks[None], shape, mode="bilinear", align_corners=False)[0]
    return masks.gt_(0.5)


def process_mask_native(protos, masks_in, bboxes, shape):
    """
    官方原生实现，先上采样，再裁剪，考虑 padding。
    """
    c, mh, mw = protos.shape
    masks = (masks_in @ protos.float().view(c, -1)).sigmoid().view(-1, mh, mw)
    # 计算填充
    gain = min(mh / shape[0], mw / shape[1])
    pad = (mw - shape[1] * gain) / 2, (mh - shape[0] * gain) / 2
    top, left = int(pad[1]), int(pad[0])
    bottom, right = int(mh - pad[1]), int(mw - pad[0])
    masks = masks[:, top:bottom, left:right]

    masks = F.interpolate(masks[None], shape, mode="bilinear", align_corners=False)[0]
    masks = crop_mask(masks, bboxes)
    return masks.gt_(0.5)


def scale_image(im1_shape, masks, im0_shape, ratio_pad=None):
    """
    将 mask 从模型输入尺寸缩放回原图尺寸。
    Args:
        im1_shape: 模型输入尺寸 [h,w]
        im0_shape: 原图尺寸 [h,w,3]
        masks: [h,w,num] mask
    """
    if ratio_pad is None:
        gain = min(im1_shape[0] / im0_shape[0], im1_shape[1] / im0_shape[1])
        pad = (im1_shape[1] - im0_shape[1] * gain) / 2, (im1_shape[0] - im0_shape[0] * gain) / 2
    else:
        pad = ratio_pad[1]
    top, left = int(pad[1]), int(pad[0])
    bottom, right = int(im1_shape[0] - pad[1]), int(im1_shape[1] - pad[0])

    masks = masks[top:bottom, left:right]
    masks = cv2.resize(masks, (im0_shape[1], im0_shape[0]))  # 缩放回原图
    if len(masks.shape) == 2:
        masks = masks[:, :, None]
    return masks


def mask_iou(mask1, mask2, eps=1e-7):
    """
    计算 mask1 与 mask2 的两两 IoU（交并比）。
    Args:
        mask1: [N, n] 预测mask
        mask2: [M, n] GT mask
    Return: IoU [N,M]
    """
    intersection = torch.matmul(mask1, mask2.t()).clamp(0)
    union = (mask1.sum(1)[:, None] + mask2.sum(1)[None]) - intersection
    return intersection / (union + eps)


def masks_iou(mask1, mask2, eps=1e-7):
    """
    计算预测 mask 与 GT mask 的逐元素 IoU。
    Args:
        mask1: [N, n]
        mask2: [N, n]
    Return: IoU (N,)
    """
    intersection = (mask1 * mask2).sum(1).clamp(0)
    union = (mask1.sum(1) + mask2.sum(1))[None] - intersection
    return intersection / (union + eps)


def masks2segments(masks, strategy="largest"):
    """
    将二值 mask 转换为多边形轮廓 (segments)。
    Args:
        masks: (n,h,w) 二值mask
        strategy:
            "concat"  → 拼接所有轮廓
            "largest" → 只取面积最大的轮廓
    Return: 多边形点集列表
    """
    segments = []
    for x in masks.int().cpu().numpy().astype("uint8"):
        c = cv2.findContours(x, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)[0]
        if c:
            if strategy == "concat":
                c = np.concatenate([x.reshape(-1, 2) for x in c])
            elif strategy == "largest":
                c = np.array(c[np.array([len(x) for x in c]).argmax()]).reshape(-1, 2)
        else:
            c = np.zeros((0, 2))
        segments.append(c.astype("float32"))
    return segments
