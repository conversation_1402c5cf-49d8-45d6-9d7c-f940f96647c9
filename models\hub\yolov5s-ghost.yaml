# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license

# Parameters
nc: 80 # number of classes
depth_multiple: 0.33 # model depth multiple
width_multiple: 0.50 # layer channel multiple
anchors:
  - [10, 13, 16, 30, 33, 23] # P3/8
  - [30, 61, 62, 45, 59, 119] # P4/16
  - [116, 90, 156, 198, 373, 326] # P5/32

# YOLOv5 v6.0 backbone
backbone:
  # [from, number, module, args]
  [
    [-1, 1, Conv, [64, 6, 2, 2]], # 0-P1/2
    [-1, 1, <PERSON><PERSON><PERSON><PERSON>, [128, 3, 2]], # 1-P2/4
    [-1, 3, C3<PERSON>hos<PERSON>, [128]],
    [-1, 1, <PERSON><PERSON><PERSON><PERSON>, [256, 3, 2]], # 3-P3/8
    [-1, 6, C3<PERSON><PERSON><PERSON>, [256]],
    [-1, 1, <PERSON><PERSON><PERSON><PERSON>, [512, 3, 2]], # 5-P4/16
    [-1, 9, C3<PERSON><PERSON><PERSON>, [512]],
    [-1, 1, <PERSON><PERSON><PERSON><PERSON>, [1024, 3, 2]], # 7-P5/32
    [-1, 3, C3<PERSON><PERSON><PERSON>, [1024]],
    [-1, 1, <PERSON><PERSON><PERSON>, [1024, 5]], # 9
  ]

# YOLOv5 v6.0 head
head: [
    [-1, 1, <PERSON><PERSON>onv, [512, 1, 1]],
    [-1, 1, nn.Upsample, [None, 2, "nearest"]],
    [[-1, 6], 1, Concat, [1]], # cat backbone P4
    [-1, 3, C3Ghost, [512, <PERSON>alse]], # 13

    [-1, 1, GhostConv, [256, 1, 1]],
    [-1, 1, nn.Upsample, [None, 2, "nearest"]],
    [[-1, 4], 1, Concat, [1]], # cat backbone P3
    [-1, 3, C3Ghost, [256, False]], # 17 (P3/8-small)

    [-1, 1, GhostConv, [256, 3, 2]],
    [[-1, 14], 1, Concat, [1]], # cat head P4
    [-1, 3, C3Ghost, [512, False]], # 20 (P4/16-medium)

    [-1, 1, GhostConv, [512, 3, 2]],
    [[-1, 10], 1, Concat, [1]], # cat head P5
    [-1, 3, C3Ghost, [1024, False]], # 23 (P5/32-large)

    [[17, 20, 23], 1, Detect, [nc, anchors]], # Detect(P3, P4, P5)
  ]
