import torch
import torch.nn as nn
from typing import Union, Tuple, Dict
import os
class InputRouter(nn.Module):
    """
    功能：在多模态/多输入模型中，按名称取对应输入。
    - forward 接受 tuple/list/dict 三种形式的多输入：
      tuple/list: (rgb, x) -> {'RGB':0, 'X':1}
      dict: {'RGB': rgb, 'X': x}
    - 该层本身不做任何变换，只把对应输入张量传下去。
    """
    def __init__(self, name: str):
        super().__init__()
        self.name = name.upper()

    def forward(self, inputs: Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor], Dict[str, torch.Tensor]]):
        if isinstance(inputs, dict):
            return inputs[self.name]
        elif isinstance(inputs, (tuple, list)):
            idx = 0 if self.name == 'RGB' else 1
            return inputs[idx]
        else:
            # 兼容单输入推理（例如只走 RGB）
            if self.name == 'RGB':
                return inputs
            raise RuntimeError(f"InputRouter 需要多输入，但只收到单输入，且要求的是 {self.name}。")